"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationSSE */ \"(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, sessionId, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        sessionId,\n        className\n    });\n    // Use SSE-based progress tracking\n    const { steps, isActive: sseActive, isComplete, error, connect, disconnect, reset } = (0,_hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE)(sessionId);\n    console.log('🎯 SSE Progress state:', {\n        steps: steps.length,\n        sseActive,\n        isActive,\n        sessionId,\n        isComplete,\n        error\n    });\n    // Create a dummy progress callback for backward compatibility\n    // The real progress tracking now happens via SSE\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Classification started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                console.log('[Progress Bridge] Classification complete (SSE mode):', roles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                console.log('[Progress Bridge] Role selection complete (SSE mode):', selectedRoles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                console.log('[Progress Bridge] Workflow selection complete (SSE mode):', workflowType);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Agent creation started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                console.log('[Progress Bridge] Agent creation complete (SSE mode):', agents.length);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Supervisor init started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                console.log('[Progress Bridge] Supervisor init complete (SSE mode):', supervisorRole);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Task planning started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                console.log('[Progress Bridge] Task planning complete (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                console.log('[Progress Bridge] Agent work started (SSE mode):', role);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                console.log('[Progress Bridge] Agent work complete (SSE mode):', role);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Supervisor synthesis started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                console.log('[Progress Bridge] Supervisor synthesis complete (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                console.log('[Progress Bridge] Orchestration complete (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                console.log('[Progress Bridge] Error (SSE mode):', step, error);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, []);\n    // Provide progress callback to parent for backward compatibility\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Reset SSE connection when not active\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (!isActive && !isComplete) {\n                reset();\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        isComplete,\n        reset\n    ]);\n    console.log('🎯 Render decision:', {\n        sseActive,\n        stepsLength: steps.length,\n        isActive,\n        sessionId,\n        shouldRender: (sseActive || steps.length > 0) && sessionId\n    });\n    // Only render if we have a session ID and either SSE is active or we have steps to show\n    if (!sessionId || !sseActive && steps.length === 0) {\n        console.log('🎯 Not rendering - sessionId:', sessionId, 'sseActive:', sseActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering SSE progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700 text-sm\",\n                    children: [\n                        \"Progress tracking error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                steps: steps,\n                isActive: sseActive,\n                autoScroll: true\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"MV7b4B3Euz5CPsltLA582nV5DWw=\", false, function() {\n    return [\n        _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});