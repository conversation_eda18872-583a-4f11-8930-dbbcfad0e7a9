"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationTracking */ \"(app-pages-browser)/./src/hooks/useOrchestrationTracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        className\n    });\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = (0,_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking)();\n    console.log('🎯 Progress state:', {\n        steps: steps.length,\n        trackingActive,\n        isActive\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'classification',\n                    stepId: 'analyzing_request',\n                    title: 'Analyzing request with RouKey AI',\n                    description: 'Understanding your request and requirements...',\n                    details: [\n                        'Processing natural language input',\n                        'Identifying task complexity and scope',\n                        'Determining specialist expertise needed',\n                        'Preparing for role classification'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('analyzing_request', [\n                    \"✅ Request analysis complete\",\n                    \"\\uD83C\\uDFAF Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83D\\uDCCA Classification threshold: \".concat(threshold),\n                    \"\\uD83D\\uDE80 Ready to prepare AI specialists\"\n                ]);\n                // Add detailed role breakdown\n                trackEvent({\n                    type: 'role_selection',\n                    stepId: 'preparing_specialists',\n                    title: 'Preparing AI Specialists',\n                    description: 'Setting up the perfect expert team for your task...',\n                    details: [\n                        \"Selected roles: \".concat(roles.join(', ')),\n                        'Configuring specialist capabilities',\n                        'Assigning API keys to each role',\n                        'Preparing collaborative workflow'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.ROLE_SELECTION,\n                    details: [\n                        \"Selected \".concat(selectedRoles.length, \" AI specialists\"),\n                        \"Each expert has been assigned their optimal tools\",\n                        \"Team assembly complete\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('role_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                const workflowNames = {\n                    'sequential': 'Step-by-step collaboration',\n                    'supervisor': 'Coordinated teamwork',\n                    'hierarchical': 'Multi-level coordination',\n                    'parallel': 'Simultaneous processing'\n                };\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.WORKFLOW_SELECTION,\n                    description: \"\".concat(workflowNames[workflowType] || 'Smart collaboration', \" approach selected\"),\n                    details: [\n                        \"Collaboration strategy: \".concat(workflowNames[workflowType] || workflowType),\n                        \"Optimized for your specific request type\",\n                        \"Team coordination plan established\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('workflow_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('agent_creation', [\n                    \"\".concat(agents.length, \" AI specialists ready to work\"),\n                    \"Each expert configured with optimal settings\",\n                    \"Team assembly complete and ready to collaborate\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_init', [\n                    'Team coordinator assigned and ready',\n                    'Communication channels established',\n                    'Ready to manage collaborative workflow'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    'Work distribution strategy finalized',\n                    'Each specialist knows their role and responsibilities',\n                    'Team ready to begin collaborative work'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    'Final response synthesis completed',\n                    'All specialist insights successfully combined',\n                    'Collaborative work finished successfully'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                completeTracking();\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n                // For testing: simulate a simple progress flow\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'classification',\n                            stepId: 'classification',\n                            title: 'Analyzing request with RouKey AI',\n                            description: 'Understanding your request and requirements...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('classification', [\n                            'Request analysis completed',\n                            'Optimal approach identified',\n                            'Ready to process your request'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 1500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'agent_creation',\n                            stepId: 'agent_creation',\n                            title: 'Preparing AI Specialist',\n                            description: 'Setting up the perfect expert for your task...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 2000);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('agent_creation', [\n                            'AI specialist ready to work',\n                            'Expert configured with optimal settings',\n                            'Processing your request...'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking,\n        trackEvent,\n        completeStep\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", false, function() {\n    return [\n        _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});