"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationTracking */ \"(app-pages-browser)/./src/hooks/useOrchestrationTracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        className\n    });\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = (0,_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking)();\n    console.log('🎯 Progress state:', {\n        steps: steps.length,\n        trackingActive,\n        isActive\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'classification',\n                    stepId: 'analyzing_request',\n                    title: 'Analyzing request with RouKey AI',\n                    description: 'Understanding your request and requirements...',\n                    details: [\n                        'Processing natural language input',\n                        'Identifying task complexity and scope',\n                        'Determining specialist expertise needed',\n                        'Preparing for role classification'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('analyzing_request', [\n                    \"✅ Request analysis complete\",\n                    \"\\uD83C\\uDFAF Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83D\\uDCCA Classification threshold: \".concat(threshold),\n                    \"\\uD83D\\uDE80 Ready to prepare AI specialists\"\n                ]);\n                // Add detailed role breakdown\n                trackEvent({\n                    type: 'role_selection',\n                    stepId: 'preparing_specialists',\n                    title: 'Preparing AI Specialists',\n                    description: 'Setting up the perfect expert team for your task...',\n                    details: [\n                        \"Selected roles: \".concat(roles.join(', ')),\n                        'Configuring specialist capabilities',\n                        'Assigning API keys to each role',\n                        'Preparing collaborative workflow'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                completeStep('preparing_specialists', [\n                    \"✅ Specialist team configured\",\n                    \"\\uD83D\\uDC65 \".concat(selectedRoles.length, \" experts selected: \").concat(selectedRoles.join(', ')),\n                    \"\\uD83D\\uDD27 API keys assigned and validated\",\n                    \"⚡ Ready for workflow orchestration\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                completeStep('orchestration_planning', [\n                    \"✅ Orchestration strategy finalized\",\n                    \"\\uD83C\\uDFAF Selected approach: \".concat(workflowType.toUpperCase()),\n                    \"\\uD83D\\uDCA1 Strategy: \".concat(reasoning),\n                    \"\\uD83D\\uDE80 Ready to create AI agents\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('agent_creation', [\n                    \"\".concat(agents.length, \" AI specialists ready to work\"),\n                    \"Each expert configured with optimal settings\",\n                    \"Team assembly complete and ready to collaborate\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_init', [\n                    'Team coordinator assigned and ready',\n                    'Communication channels established',\n                    'Ready to manage collaborative workflow'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    'Work distribution strategy finalized',\n                    'Each specialist knows their role and responsibilities',\n                    'Team ready to begin collaborative work'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    'Final response synthesis completed',\n                    'All specialist insights successfully combined',\n                    'Collaborative work finished successfully'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                completeTracking();\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n                // For testing: simulate a simple progress flow\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'classification',\n                            stepId: 'classification',\n                            title: 'Analyzing request with RouKey AI',\n                            description: 'Understanding your request and requirements...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('classification', [\n                            'Request analysis completed',\n                            'Optimal approach identified',\n                            'Ready to process your request'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 1500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'agent_creation',\n                            stepId: 'agent_creation',\n                            title: 'Preparing AI Specialist',\n                            description: 'Setting up the perfect expert for your task...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 2000);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('agent_creation', [\n                            'AI specialist ready to work',\n                            'Expert configured with optimal settings',\n                            'Processing your request...'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking,\n        trackEvent,\n        completeStep\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 272,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", false, function() {\n    return [\n        _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFc0Q7QUFDb0I7QUFDdUI7QUFTbEYsU0FBU00sNEJBQTRCLEtBSWpCO1FBSmlCLEVBQ2xEQyxRQUFRLEVBQ1JDLGtCQUFrQixFQUNsQkMsWUFBWSxFQUFFLEVBQ21CLEdBSmlCOztJQUtsREMsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQztRQUFFSjtRQUFVRTtJQUFVO0lBRTVFLE1BQU0sRUFDSkcsS0FBSyxFQUNMTCxVQUFVTSxjQUFjLEVBQ3hCQyxVQUFVLEVBQ1ZDLFlBQVksRUFDWkMsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDaEJDLGFBQWEsRUFDZCxHQUFHZCx5RkFBd0JBO0lBRTVCTSxRQUFRQyxHQUFHLENBQUMsc0JBQXNCO1FBQUVDLE9BQU9BLE1BQU1PLE1BQU07UUFBRU47UUFBZ0JOO0lBQVM7SUFFbEYsb0RBQW9EO0lBQ3BELE1BQU1hLG1CQUFxQ2xCLGtEQUFXQSxDQUFDO1FBQ3JEbUIscUJBQXFCO3lFQUFFO2dCQUNyQlAsV0FBVztvQkFDVFEsTUFBTTtvQkFDTkMsUUFBUTtvQkFDUkMsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUzt3QkFDUDt3QkFDQTt3QkFDQTt3QkFDQTtxQkFDRDtnQkFDSDtZQUNGOztRQUVBQyx3QkFBd0I7eUVBQUUsQ0FBQ0MsT0FBaUJDO2dCQUMxQ2QsYUFBYSxxQkFBcUI7b0JBQy9CO29CQUNBLDJCQUE2QixPQUFiYSxNQUFNVCxNQUFNLEVBQUM7b0JBQzdCLDBDQUF5QyxPQUFWVTtvQkFDL0I7aUJBQ0Y7Z0JBRUQsOEJBQThCO2dCQUM5QmYsV0FBVztvQkFDVFEsTUFBTTtvQkFDTkMsUUFBUTtvQkFDUkMsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUzt3QkFDTixtQkFBbUMsT0FBakJFLE1BQU1FLElBQUksQ0FBQzt3QkFDOUI7d0JBQ0E7d0JBQ0E7cUJBQ0Q7Z0JBQ0g7WUFDRjs7UUFFQUMsdUJBQXVCO3lFQUFFLENBQUNDLGVBQXlCQztnQkFDakRsQixhQUFhLHlCQUF5QjtvQkFDbkM7b0JBQ0EsZ0JBQStDaUIsT0FBMUNBLGNBQWNiLE1BQU0sRUFBQyx1QkFBOEMsT0FBekJhLGNBQWNGLElBQUksQ0FBQztvQkFDbEU7b0JBQ0E7aUJBQ0Y7WUFDSDs7UUFFQUksMkJBQTJCO3lFQUFFLENBQUNDLGNBQXNCQztnQkFDbERyQixhQUFhLDBCQUEwQjtvQkFDcEM7b0JBQ0EsbUNBQW1ELE9BQTNCb0IsYUFBYUUsV0FBVztvQkFDaEQsMEJBQXlCLE9BQVZEO29CQUNmO2lCQUNGO1lBQ0g7O1FBRUFFLG9CQUFvQjt5RUFBRTtnQkFDcEJ4QixXQUFXVCxnRkFBbUJBLENBQUNrQyxVQUFVLENBQUNDLGNBQWM7WUFDMUQ7O1FBRUFDLHVCQUF1Qjt5RUFBRSxDQUFDQztnQkFDeEIzQixhQUFhLGtCQUFrQjtvQkFDNUIsR0FBZ0IsT0FBZDJCLE9BQU92QixNQUFNLEVBQUM7b0JBQ2hCO29CQUNBO2lCQUNGO1lBQ0g7O1FBRUF3QixxQkFBcUI7eUVBQUU7Z0JBQ3JCN0IsV0FBV1QsZ0ZBQW1CQSxDQUFDa0MsVUFBVSxDQUFDSyxlQUFlO1lBQzNEOztRQUVBQyx3QkFBd0I7eUVBQUUsQ0FBQ0M7Z0JBQ3pCL0IsYUFBYSxtQkFBbUI7b0JBQzlCO29CQUNBO29CQUNBO2lCQUNEO1lBQ0g7O1FBRUFnQyxtQkFBbUI7eUVBQUU7Z0JBQ25CakMsV0FBV1QsZ0ZBQW1CQSxDQUFDa0MsVUFBVSxDQUFDUyxhQUFhO1lBQ3pEOztRQUVBQyxzQkFBc0I7eUVBQUUsQ0FBQ0M7Z0JBQ3ZCbkMsYUFBYSxpQkFBaUI7b0JBQzVCO29CQUNBO29CQUNBO2lCQUNEO1lBQ0g7O1FBRUFvQyxnQkFBZ0I7eUVBQUUsQ0FBQ0MsTUFBY0M7Z0JBQy9CLE1BQU1DLFlBQVk7b0JBQ2hCLDBCQUEwQjtvQkFDMUIsV0FBVztvQkFDWCxrQkFBa0I7b0JBQ2xCLG1CQUFtQjtvQkFDbkIsZ0JBQWdCO2dCQUNsQjtnQkFFQXhDLFdBQVc7b0JBQ1RRLE1BQU07b0JBQ05DLFFBQVEsU0FBYyxPQUFMNkI7b0JBQ2pCNUIsT0FBTyxHQUFnRSxPQUE3RDhCLFNBQVMsQ0FBQ0YsS0FBK0IsSUFBSSxpQkFBZ0I7b0JBQ3ZFM0IsYUFBYTRCO29CQUNiM0IsU0FBUzt3QkFDTixlQUFnRSxPQUFsRDRCLFNBQVMsQ0FBQ0YsS0FBK0IsSUFBSUE7d0JBQzVEO3dCQUNBO3FCQUNEO2dCQUNIO1lBQ0Y7O1FBRUFHLG1CQUFtQjt5RUFBRSxDQUFDSCxNQUFjSTtnQkFDbEMsTUFBTUYsWUFBWTtvQkFDaEIsMEJBQTBCO29CQUMxQixXQUFXO29CQUNYLGtCQUFrQjtvQkFDbEIsbUJBQW1CO29CQUNuQixnQkFBZ0I7Z0JBQ2xCO2dCQUVBdkMsYUFBYSxTQUFjLE9BQUxxQyxPQUFRO29CQUMzQixHQUE0RCxPQUExREUsU0FBUyxDQUFDRixLQUErQixJQUFJLGNBQWE7b0JBQzVEO29CQUNEO2lCQUNEO1lBQ0g7O1FBRUFLLDBCQUEwQjt5RUFBRTtnQkFDMUIzQyxXQUFXO29CQUNUUSxNQUFNO29CQUNOQyxRQUFRO29CQUNSQyxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO3dCQUNQO3dCQUNBO3dCQUNBO3FCQUNEO2dCQUNIO1lBQ0Y7O1FBRUFnQyw2QkFBNkI7eUVBQUUsQ0FBQ0M7Z0JBQzlCNUMsYUFBYSx3QkFBd0I7b0JBQ25DO29CQUNBO29CQUNBO2lCQUNEO1lBQ0g7O1FBRUE2Qyx1QkFBdUI7eUVBQUUsQ0FBQ0o7Z0JBQ3hCdkM7WUFDRjs7UUFFQTRDLE9BQU87eUVBQUUsQ0FBQ0MsTUFBY0M7Z0JBQ3RCakQsV0FBVztvQkFDVFEsTUFBTTtvQkFDTkMsUUFBUSxTQUFjLE9BQUx1QztvQkFDakJ0QyxPQUFPLFlBQWlCLE9BQUxzQztvQkFDbkJyQyxhQUFhc0M7b0JBQ2JyQyxTQUFTO3dCQUNOLFNBQWEsT0FBTG9DO3dCQUNSLFVBQWUsT0FBTkM7d0JBQ1Y7cUJBQ0Q7Z0JBQ0g7WUFDRjs7SUFDRixHQUFHO1FBQUNqRDtRQUFZQztRQUFjRTtLQUFpQjtJQUUvQyxzQ0FBc0M7SUFDdENoQixnREFBU0E7aURBQUM7WUFDUixJQUFJTyxvQkFBb0I7Z0JBQ3RCQSxtQkFBbUJZO1lBQ3JCO1FBQ0Y7Z0RBQUc7UUFBQ1o7UUFBb0JZO0tBQWlCO0lBRXpDLHdDQUF3QztJQUN4Q25CLGdEQUFTQTtpREFBQztZQUNSLElBQUlNLFlBQVksQ0FBQ00sZ0JBQWdCO2dCQUMvQkc7Z0JBRUEsK0NBQStDO2dCQUMvQ2dEOzZEQUFXO3dCQUNUbEQsV0FBVzs0QkFDVFEsTUFBTTs0QkFDTkMsUUFBUTs0QkFDUkMsT0FBTzs0QkFDUEMsYUFBYTt3QkFDZjtvQkFDRjs0REFBRztnQkFFSHVDOzZEQUFXO3dCQUNUakQsYUFBYSxrQkFBa0I7NEJBQzdCOzRCQUNBOzRCQUNBO3lCQUNEO29CQUNIOzREQUFHO2dCQUVIaUQ7NkRBQVc7d0JBQ1RsRCxXQUFXOzRCQUNUUSxNQUFNOzRCQUNOQyxRQUFROzRCQUNSQyxPQUFPOzRCQUNQQyxhQUFhO3dCQUNmO29CQUNGOzREQUFHO2dCQUVIdUM7NkRBQVc7d0JBQ1RqRCxhQUFhLGtCQUFrQjs0QkFDN0I7NEJBQ0E7NEJBQ0E7eUJBQ0Q7b0JBQ0g7NERBQUc7WUFFTCxPQUFPLElBQUksQ0FBQ1IsWUFBWU0sZ0JBQWdCO2dCQUN0QyxtREFBbUQ7Z0JBQ25EbUQ7NkRBQVc7d0JBQ1Q5QztvQkFDRjs0REFBRztZQUNMO1FBQ0Y7Z0RBQUc7UUFBQ1g7UUFBVU07UUFBZ0JHO1FBQWVFO1FBQWVKO1FBQVlDO0tBQWE7SUFFckZMLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUI7UUFBRUU7UUFBZ0JvRCxhQUFhckQsTUFBTU8sTUFBTTtRQUFFK0MsY0FBY3JELGtCQUFrQkQsTUFBTU8sTUFBTSxHQUFHO0lBQUU7SUFFakksSUFBSSxDQUFDTixrQkFBa0JELE1BQU1PLE1BQU0sS0FBSyxHQUFHO1FBQ3pDVCxRQUFRQyxHQUFHLENBQUMsc0NBQXNDRSxnQkFBZ0IsVUFBVUQsTUFBTU8sTUFBTTtRQUN4RixPQUFPO0lBQ1Q7SUFFQVQsUUFBUUMsR0FBRyxDQUFDLDZDQUE2Q0M7SUFFekQscUJBQ0UsOERBQUN1RDtRQUFJMUQsV0FBV0E7a0JBQ2QsNEVBQUNOLHFFQUE0QkE7WUFDM0JTLE9BQU9BO1lBQ1BMLFVBQVVNO1lBQ1Z1RCxZQUFZOzs7Ozs7Ozs7OztBQUlwQjtHQXpRd0I5RDs7UUFlbEJGLHFGQUF3QkE7OztLQWZORSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGNvbXBvbmVudHNcXE9yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBPcmNoZXN0cmF0aW9uUHJvZ3Jlc3NUcmFja2VyIGZyb20gJy4vT3JjaGVzdHJhdGlvblByb2dyZXNzVHJhY2tlcic7XG5pbXBvcnQgeyB1c2VPcmNoZXN0cmF0aW9uVHJhY2tpbmcsIE9SQ0hFU1RSQVRJT05fU1RFUFMgfSBmcm9tICdAL2hvb2tzL3VzZU9yY2hlc3RyYXRpb25UcmFja2luZyc7XG5pbXBvcnQgeyB0eXBlIFByb2dyZXNzQ2FsbGJhY2sgfSBmcm9tICdAL2xpYi9sYW5nZ3JhcGgtb3JjaGVzdHJhdGlvbi9Sb3VLZXlMYW5nR3JhcGhJbnRlZ3JhdGlvbic7XG5cbmludGVyZmFjZSBPcmNoZXN0cmF0aW9uUHJvZ3Jlc3NCcmlkZ2VQcm9wcyB7XG4gIGlzQWN0aXZlOiBib29sZWFuO1xuICBvblByb2dyZXNzQ2FsbGJhY2s/OiAoY2FsbGJhY2s6IFByb2dyZXNzQ2FsbGJhY2spID0+IHZvaWQ7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gT3JjaGVzdHJhdGlvblByb2dyZXNzQnJpZGdlKHtcbiAgaXNBY3RpdmUsXG4gIG9uUHJvZ3Jlc3NDYWxsYmFjayxcbiAgY2xhc3NOYW1lID0gJydcbn06IE9yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZVByb3BzKSB7XG4gIGNvbnNvbGUubG9nKCfwn46vIE9yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZSByZW5kZXI6JywgeyBpc0FjdGl2ZSwgY2xhc3NOYW1lIH0pO1xuXG4gIGNvbnN0IHtcbiAgICBzdGVwcyxcbiAgICBpc0FjdGl2ZTogdHJhY2tpbmdBY3RpdmUsXG4gICAgdHJhY2tFdmVudCxcbiAgICBjb21wbGV0ZVN0ZXAsXG4gICAgc3RhcnRUcmFja2luZyxcbiAgICBjb21wbGV0ZVRyYWNraW5nLFxuICAgIHJlc2V0VHJhY2tpbmdcbiAgfSA9IHVzZU9yY2hlc3RyYXRpb25UcmFja2luZygpO1xuXG4gIGNvbnNvbGUubG9nKCfwn46vIFByb2dyZXNzIHN0YXRlOicsIHsgc3RlcHM6IHN0ZXBzLmxlbmd0aCwgdHJhY2tpbmdBY3RpdmUsIGlzQWN0aXZlIH0pO1xuXG4gIC8vIENyZWF0ZSBwcm9ncmVzcyBjYWxsYmFjayBmb3Igb3JjaGVzdHJhdGlvbiBzeXN0ZW1cbiAgY29uc3QgcHJvZ3Jlc3NDYWxsYmFjazogUHJvZ3Jlc3NDYWxsYmFjayA9IHVzZUNhbGxiYWNrKHtcbiAgICBvbkNsYXNzaWZpY2F0aW9uU3RhcnQ6ICgpID0+IHtcbiAgICAgIHRyYWNrRXZlbnQoe1xuICAgICAgICB0eXBlOiAnY2xhc3NpZmljYXRpb24nLFxuICAgICAgICBzdGVwSWQ6ICdhbmFseXppbmdfcmVxdWVzdCcsXG4gICAgICAgIHRpdGxlOiAnQW5hbHl6aW5nIHJlcXVlc3Qgd2l0aCBSb3VLZXkgQUknLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ1VuZGVyc3RhbmRpbmcgeW91ciByZXF1ZXN0IGFuZCByZXF1aXJlbWVudHMuLi4nLFxuICAgICAgICBkZXRhaWxzOiBbXG4gICAgICAgICAgJ1Byb2Nlc3NpbmcgbmF0dXJhbCBsYW5ndWFnZSBpbnB1dCcsXG4gICAgICAgICAgJ0lkZW50aWZ5aW5nIHRhc2sgY29tcGxleGl0eSBhbmQgc2NvcGUnLFxuICAgICAgICAgICdEZXRlcm1pbmluZyBzcGVjaWFsaXN0IGV4cGVydGlzZSBuZWVkZWQnLFxuICAgICAgICAgICdQcmVwYXJpbmcgZm9yIHJvbGUgY2xhc3NpZmljYXRpb24nXG4gICAgICAgIF1cbiAgICAgIH0pO1xuICAgIH0sXG5cbiAgICBvbkNsYXNzaWZpY2F0aW9uQ29tcGxldGU6IChyb2xlczogc3RyaW5nW10sIHRocmVzaG9sZDogbnVtYmVyKSA9PiB7XG4gICAgICBjb21wbGV0ZVN0ZXAoJ2FuYWx5emluZ19yZXF1ZXN0JywgW1xuICAgICAgICBg4pyFIFJlcXVlc3QgYW5hbHlzaXMgY29tcGxldGVgLFxuICAgICAgICBg8J+OryBJZGVudGlmaWVkICR7cm9sZXMubGVuZ3RofSBzcGVjaWFsaXN0IGFyZWFzIG5lZWRlZGAsXG4gICAgICAgIGDwn5OKIENsYXNzaWZpY2F0aW9uIHRocmVzaG9sZDogJHt0aHJlc2hvbGR9YCxcbiAgICAgICAgYPCfmoAgUmVhZHkgdG8gcHJlcGFyZSBBSSBzcGVjaWFsaXN0c2BcbiAgICAgIF0pO1xuXG4gICAgICAvLyBBZGQgZGV0YWlsZWQgcm9sZSBicmVha2Rvd25cbiAgICAgIHRyYWNrRXZlbnQoe1xuICAgICAgICB0eXBlOiAncm9sZV9zZWxlY3Rpb24nLFxuICAgICAgICBzdGVwSWQ6ICdwcmVwYXJpbmdfc3BlY2lhbGlzdHMnLFxuICAgICAgICB0aXRsZTogJ1ByZXBhcmluZyBBSSBTcGVjaWFsaXN0cycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnU2V0dGluZyB1cCB0aGUgcGVyZmVjdCBleHBlcnQgdGVhbSBmb3IgeW91ciB0YXNrLi4uJyxcbiAgICAgICAgZGV0YWlsczogW1xuICAgICAgICAgIGBTZWxlY3RlZCByb2xlczogJHtyb2xlcy5qb2luKCcsICcpfWAsXG4gICAgICAgICAgJ0NvbmZpZ3VyaW5nIHNwZWNpYWxpc3QgY2FwYWJpbGl0aWVzJyxcbiAgICAgICAgICAnQXNzaWduaW5nIEFQSSBrZXlzIHRvIGVhY2ggcm9sZScsXG4gICAgICAgICAgJ1ByZXBhcmluZyBjb2xsYWJvcmF0aXZlIHdvcmtmbG93J1xuICAgICAgICBdXG4gICAgICB9KTtcbiAgICB9LFxuICAgIFxuICAgIG9uUm9sZVNlbGVjdGlvbkNvbXBsZXRlOiAoc2VsZWN0ZWRSb2xlczogc3RyaW5nW10sIGZpbHRlcmVkUm9sZXM6IHN0cmluZ1tdKSA9PiB7XG4gICAgICBjb21wbGV0ZVN0ZXAoJ3ByZXBhcmluZ19zcGVjaWFsaXN0cycsIFtcbiAgICAgICAgYOKchSBTcGVjaWFsaXN0IHRlYW0gY29uZmlndXJlZGAsXG4gICAgICAgIGDwn5GlICR7c2VsZWN0ZWRSb2xlcy5sZW5ndGh9IGV4cGVydHMgc2VsZWN0ZWQ6ICR7c2VsZWN0ZWRSb2xlcy5qb2luKCcsICcpfWAsXG4gICAgICAgIGDwn5SnIEFQSSBrZXlzIGFzc2lnbmVkIGFuZCB2YWxpZGF0ZWRgLFxuICAgICAgICBg4pqhIFJlYWR5IGZvciB3b3JrZmxvdyBvcmNoZXN0cmF0aW9uYFxuICAgICAgXSk7XG4gICAgfSxcbiAgICBcbiAgICBvbldvcmtmbG93U2VsZWN0aW9uQ29tcGxldGU6ICh3b3JrZmxvd1R5cGU6IHN0cmluZywgcmVhc29uaW5nOiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbXBsZXRlU3RlcCgnb3JjaGVzdHJhdGlvbl9wbGFubmluZycsIFtcbiAgICAgICAgYOKchSBPcmNoZXN0cmF0aW9uIHN0cmF0ZWd5IGZpbmFsaXplZGAsXG4gICAgICAgIGDwn46vIFNlbGVjdGVkIGFwcHJvYWNoOiAke3dvcmtmbG93VHlwZS50b1VwcGVyQ2FzZSgpfWAsXG4gICAgICAgIGDwn5KhIFN0cmF0ZWd5OiAke3JlYXNvbmluZ31gLFxuICAgICAgICBg8J+agCBSZWFkeSB0byBjcmVhdGUgQUkgYWdlbnRzYFxuICAgICAgXSk7XG4gICAgfSxcbiAgICBcbiAgICBvbkFnZW50Q3JlYXRpb25TdGFydDogKCkgPT4ge1xuICAgICAgdHJhY2tFdmVudChPUkNIRVNUUkFUSU9OX1NURVBTLk1VTFRJX1JPTEUuQUdFTlRfQ1JFQVRJT04pO1xuICAgIH0sXG4gICAgXG4gICAgb25BZ2VudENyZWF0aW9uQ29tcGxldGU6IChhZ2VudHM6IEFycmF5PHsgcm9sZTogc3RyaW5nLCBhcGlLZXk6IHN0cmluZyB9PikgPT4ge1xuICAgICAgY29tcGxldGVTdGVwKCdhZ2VudF9jcmVhdGlvbicsIFtcbiAgICAgICAgYCR7YWdlbnRzLmxlbmd0aH0gQUkgc3BlY2lhbGlzdHMgcmVhZHkgdG8gd29ya2AsXG4gICAgICAgIGBFYWNoIGV4cGVydCBjb25maWd1cmVkIHdpdGggb3B0aW1hbCBzZXR0aW5nc2AsXG4gICAgICAgIGBUZWFtIGFzc2VtYmx5IGNvbXBsZXRlIGFuZCByZWFkeSB0byBjb2xsYWJvcmF0ZWBcbiAgICAgIF0pO1xuICAgIH0sXG4gICAgXG4gICAgb25TdXBlcnZpc29ySW5pdFN0YXJ0OiAoKSA9PiB7XG4gICAgICB0cmFja0V2ZW50KE9SQ0hFU1RSQVRJT05fU1RFUFMuTVVMVElfUk9MRS5TVVBFUlZJU09SX0lOSVQpO1xuICAgIH0sXG4gICAgXG4gICAgb25TdXBlcnZpc29ySW5pdENvbXBsZXRlOiAoc3VwZXJ2aXNvclJvbGU6IHN0cmluZykgPT4ge1xuICAgICAgY29tcGxldGVTdGVwKCdzdXBlcnZpc29yX2luaXQnLCBbXG4gICAgICAgICdUZWFtIGNvb3JkaW5hdG9yIGFzc2lnbmVkIGFuZCByZWFkeScsXG4gICAgICAgICdDb21tdW5pY2F0aW9uIGNoYW5uZWxzIGVzdGFibGlzaGVkJyxcbiAgICAgICAgJ1JlYWR5IHRvIG1hbmFnZSBjb2xsYWJvcmF0aXZlIHdvcmtmbG93J1xuICAgICAgXSk7XG4gICAgfSxcbiAgICBcbiAgICBvblRhc2tQbGFubmluZ1N0YXJ0OiAoKSA9PiB7XG4gICAgICB0cmFja0V2ZW50KE9SQ0hFU1RSQVRJT05fU1RFUFMuTVVMVElfUk9MRS5UQVNLX1BMQU5OSU5HKTtcbiAgICB9LFxuICAgIFxuICAgIG9uVGFza1BsYW5uaW5nQ29tcGxldGU6IChwbGFuOiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbXBsZXRlU3RlcCgndGFza19wbGFubmluZycsIFtcbiAgICAgICAgJ1dvcmsgZGlzdHJpYnV0aW9uIHN0cmF0ZWd5IGZpbmFsaXplZCcsXG4gICAgICAgICdFYWNoIHNwZWNpYWxpc3Qga25vd3MgdGhlaXIgcm9sZSBhbmQgcmVzcG9uc2liaWxpdGllcycsXG4gICAgICAgICdUZWFtIHJlYWR5IHRvIGJlZ2luIGNvbGxhYm9yYXRpdmUgd29yaydcbiAgICAgIF0pO1xuICAgIH0sXG4gICAgXG4gICAgb25BZ2VudFdvcmtTdGFydDogKHJvbGU6IHN0cmluZywgdGFzazogc3RyaW5nKSA9PiB7XG4gICAgICBjb25zdCByb2xlTmFtZXMgPSB7XG4gICAgICAgICdicmFpbnN0b3JtaW5nX2lkZWF0aW9uJzogJ0NyZWF0aXZlIElkZWF0aW9uIFNwZWNpYWxpc3QnLFxuICAgICAgICAnd3JpdGluZyc6ICdDb250ZW50IFdyaXRpbmcgRXhwZXJ0JyxcbiAgICAgICAgJ2NvZGluZ19iYWNrZW5kJzogJ0JhY2tlbmQgRGV2ZWxvcG1lbnQgRXhwZXJ0JyxcbiAgICAgICAgJ2NvZGluZ19mcm9udGVuZCc6ICdGcm9udGVuZCBEZXZlbG9wbWVudCBFeHBlcnQnLFxuICAgICAgICAnZ2VuZXJhbF9jaGF0JzogJ0dlbmVyYWwgQUkgQXNzaXN0YW50J1xuICAgICAgfTtcbiAgICAgIFxuICAgICAgdHJhY2tFdmVudCh7XG4gICAgICAgIHR5cGU6ICdhZ2VudF93b3JraW5nJyxcbiAgICAgICAgc3RlcElkOiBgYWdlbnRfJHtyb2xlfWAsXG4gICAgICAgIHRpdGxlOiBgJHtyb2xlTmFtZXNbcm9sZSBhcyBrZXlvZiB0eXBlb2Ygcm9sZU5hbWVzXSB8fCAnQUkgU3BlY2lhbGlzdCd9IFdvcmtpbmdgLFxuICAgICAgICBkZXNjcmlwdGlvbjogdGFzayxcbiAgICAgICAgZGV0YWlsczogW1xuICAgICAgICAgIGBTcGVjaWFsaXN0OiAke3JvbGVOYW1lc1tyb2xlIGFzIGtleW9mIHR5cGVvZiByb2xlTmFtZXNdIHx8IHJvbGV9YCxcbiAgICAgICAgICAnQXBwbHlpbmcgZXhwZXJ0aXNlIHRvIHlvdXIgcmVxdWVzdCcsXG4gICAgICAgICAgJ1Byb2Nlc3NpbmcgYW5kIGdlbmVyYXRpbmcgcmVzcG9uc2UuLi4nXG4gICAgICAgIF1cbiAgICAgIH0pO1xuICAgIH0sXG4gICAgXG4gICAgb25BZ2VudFdvcmtDb21wbGV0ZTogKHJvbGU6IHN0cmluZywgcmVzdWx0OiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbnN0IHJvbGVOYW1lcyA9IHtcbiAgICAgICAgJ2JyYWluc3Rvcm1pbmdfaWRlYXRpb24nOiAnQ3JlYXRpdmUgSWRlYXRpb24gU3BlY2lhbGlzdCcsXG4gICAgICAgICd3cml0aW5nJzogJ0NvbnRlbnQgV3JpdGluZyBFeHBlcnQnLFxuICAgICAgICAnY29kaW5nX2JhY2tlbmQnOiAnQmFja2VuZCBEZXZlbG9wbWVudCBFeHBlcnQnLFxuICAgICAgICAnY29kaW5nX2Zyb250ZW5kJzogJ0Zyb250ZW5kIERldmVsb3BtZW50IEV4cGVydCcsXG4gICAgICAgICdnZW5lcmFsX2NoYXQnOiAnR2VuZXJhbCBBSSBBc3Npc3RhbnQnXG4gICAgICB9O1xuICAgICAgXG4gICAgICBjb21wbGV0ZVN0ZXAoYGFnZW50XyR7cm9sZX1gLCBbXG4gICAgICAgIGAke3JvbGVOYW1lc1tyb2xlIGFzIGtleW9mIHR5cGVvZiByb2xlTmFtZXNdIHx8ICdTcGVjaWFsaXN0J30gd29yayBjb21wbGV0ZWRgLFxuICAgICAgICBgSGlnaC1xdWFsaXR5IHJlc3BvbnNlIGdlbmVyYXRlZGAsXG4gICAgICAgICdDb250cmlidXRpb24gcmVhZHkgZm9yIHRlYW0gaW50ZWdyYXRpb24nXG4gICAgICBdKTtcbiAgICB9LFxuICAgIFxuICAgIG9uU3VwZXJ2aXNvclN5bnRoZXNpc1N0YXJ0OiAoKSA9PiB7XG4gICAgICB0cmFja0V2ZW50KHtcbiAgICAgICAgdHlwZTogJ3N1cGVydmlzb3Jfc3ludGhlc2lzJyxcbiAgICAgICAgc3RlcElkOiAnc3VwZXJ2aXNvcl9zeW50aGVzaXMnLFxuICAgICAgICB0aXRsZTogJ1RlYW0gQ29vcmRpbmF0b3I6IENvbWJpbmluZyBSZXN1bHRzJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdDcmVhdGluZyBjb21wcmVoZW5zaXZlIGZpbmFsIHJlc3BvbnNlLi4uJyxcbiAgICAgICAgZGV0YWlsczogW1xuICAgICAgICAgICdSZXZpZXdpbmcgYWxsIHNwZWNpYWxpc3QgY29udHJpYnV0aW9ucycsXG4gICAgICAgICAgJ0NvbWJpbmluZyBpbnNpZ2h0cyBpbnRvIHVuaWZpZWQgcmVzcG9uc2UnLFxuICAgICAgICAgICdFbnN1cmluZyBxdWFsaXR5IGFuZCBjb2hlcmVuY2UnXG4gICAgICAgIF1cbiAgICAgIH0pO1xuICAgIH0sXG4gICAgXG4gICAgb25TdXBlcnZpc29yU3ludGhlc2lzQ29tcGxldGU6IChzeW50aGVzaXM6IHN0cmluZykgPT4ge1xuICAgICAgY29tcGxldGVTdGVwKCdzdXBlcnZpc29yX3N5bnRoZXNpcycsIFtcbiAgICAgICAgJ0ZpbmFsIHJlc3BvbnNlIHN5bnRoZXNpcyBjb21wbGV0ZWQnLFxuICAgICAgICAnQWxsIHNwZWNpYWxpc3QgaW5zaWdodHMgc3VjY2Vzc2Z1bGx5IGNvbWJpbmVkJyxcbiAgICAgICAgJ0NvbGxhYm9yYXRpdmUgd29yayBmaW5pc2hlZCBzdWNjZXNzZnVsbHknXG4gICAgICBdKTtcbiAgICB9LFxuICAgIFxuICAgIG9uT3JjaGVzdHJhdGlvbkNvbXBsZXRlOiAocmVzdWx0OiBhbnkpID0+IHtcbiAgICAgIGNvbXBsZXRlVHJhY2tpbmcoKTtcbiAgICB9LFxuICAgIFxuICAgIG9uRXJyb3I6IChzdGVwOiBzdHJpbmcsIGVycm9yOiBzdHJpbmcpID0+IHtcbiAgICAgIHRyYWNrRXZlbnQoe1xuICAgICAgICB0eXBlOiAnZXJyb3InLFxuICAgICAgICBzdGVwSWQ6IGBlcnJvcl8ke3N0ZXB9YCxcbiAgICAgICAgdGl0bGU6IGBFcnJvciBpbiAke3N0ZXB9YCxcbiAgICAgICAgZGVzY3JpcHRpb246IGVycm9yLFxuICAgICAgICBkZXRhaWxzOiBbXG4gICAgICAgICAgYFN0ZXA6ICR7c3RlcH1gLFxuICAgICAgICAgIGBFcnJvcjogJHtlcnJvcn1gLFxuICAgICAgICAgICdBdHRlbXB0aW5nIHJlY292ZXJ5Li4uJ1xuICAgICAgICBdXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFt0cmFja0V2ZW50LCBjb21wbGV0ZVN0ZXAsIGNvbXBsZXRlVHJhY2tpbmddKTtcblxuICAvLyBQcm92aWRlIHByb2dyZXNzIGNhbGxiYWNrIHRvIHBhcmVudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChvblByb2dyZXNzQ2FsbGJhY2spIHtcbiAgICAgIG9uUHJvZ3Jlc3NDYWxsYmFjayhwcm9ncmVzc0NhbGxiYWNrKTtcbiAgICB9XG4gIH0sIFtvblByb2dyZXNzQ2FsbGJhY2ssIHByb2dyZXNzQ2FsbGJhY2tdKTtcblxuICAvLyBTdGFydC9zdG9wIHRyYWNraW5nIGJhc2VkIG9uIGlzQWN0aXZlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzQWN0aXZlICYmICF0cmFja2luZ0FjdGl2ZSkge1xuICAgICAgc3RhcnRUcmFja2luZygpO1xuXG4gICAgICAvLyBGb3IgdGVzdGluZzogc2ltdWxhdGUgYSBzaW1wbGUgcHJvZ3Jlc3MgZmxvd1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHRyYWNrRXZlbnQoe1xuICAgICAgICAgIHR5cGU6ICdjbGFzc2lmaWNhdGlvbicsXG4gICAgICAgICAgc3RlcElkOiAnY2xhc3NpZmljYXRpb24nLFxuICAgICAgICAgIHRpdGxlOiAnQW5hbHl6aW5nIHJlcXVlc3Qgd2l0aCBSb3VLZXkgQUknLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnVW5kZXJzdGFuZGluZyB5b3VyIHJlcXVlc3QgYW5kIHJlcXVpcmVtZW50cy4uLidcbiAgICAgICAgfSk7XG4gICAgICB9LCA1MDApO1xuXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgY29tcGxldGVTdGVwKCdjbGFzc2lmaWNhdGlvbicsIFtcbiAgICAgICAgICAnUmVxdWVzdCBhbmFseXNpcyBjb21wbGV0ZWQnLFxuICAgICAgICAgICdPcHRpbWFsIGFwcHJvYWNoIGlkZW50aWZpZWQnLFxuICAgICAgICAgICdSZWFkeSB0byBwcm9jZXNzIHlvdXIgcmVxdWVzdCdcbiAgICAgICAgXSk7XG4gICAgICB9LCAxNTAwKTtcblxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHRyYWNrRXZlbnQoe1xuICAgICAgICAgIHR5cGU6ICdhZ2VudF9jcmVhdGlvbicsXG4gICAgICAgICAgc3RlcElkOiAnYWdlbnRfY3JlYXRpb24nLFxuICAgICAgICAgIHRpdGxlOiAnUHJlcGFyaW5nIEFJIFNwZWNpYWxpc3QnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnU2V0dGluZyB1cCB0aGUgcGVyZmVjdCBleHBlcnQgZm9yIHlvdXIgdGFzay4uLidcbiAgICAgICAgfSk7XG4gICAgICB9LCAyMDAwKTtcblxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGNvbXBsZXRlU3RlcCgnYWdlbnRfY3JlYXRpb24nLCBbXG4gICAgICAgICAgJ0FJIHNwZWNpYWxpc3QgcmVhZHkgdG8gd29yaycsXG4gICAgICAgICAgJ0V4cGVydCBjb25maWd1cmVkIHdpdGggb3B0aW1hbCBzZXR0aW5ncycsXG4gICAgICAgICAgJ1Byb2Nlc3NpbmcgeW91ciByZXF1ZXN0Li4uJ1xuICAgICAgICBdKTtcbiAgICAgIH0sIDMwMDApO1xuXG4gICAgfSBlbHNlIGlmICghaXNBY3RpdmUgJiYgdHJhY2tpbmdBY3RpdmUpIHtcbiAgICAgIC8vIFNtYWxsIGRlbGF5IGJlZm9yZSByZXNldHRpbmcgdG8gc2hvdyBmaW5hbCBzdGF0ZVxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHJlc2V0VHJhY2tpbmcoKTtcbiAgICAgIH0sIDMwMDApO1xuICAgIH1cbiAgfSwgW2lzQWN0aXZlLCB0cmFja2luZ0FjdGl2ZSwgc3RhcnRUcmFja2luZywgcmVzZXRUcmFja2luZywgdHJhY2tFdmVudCwgY29tcGxldGVTdGVwXSk7XG5cbiAgY29uc29sZS5sb2coJ/Cfjq8gUmVuZGVyIGRlY2lzaW9uOicsIHsgdHJhY2tpbmdBY3RpdmUsIHN0ZXBzTGVuZ3RoOiBzdGVwcy5sZW5ndGgsIHNob3VsZFJlbmRlcjogdHJhY2tpbmdBY3RpdmUgJiYgc3RlcHMubGVuZ3RoID4gMCB9KTtcblxuICBpZiAoIXRyYWNraW5nQWN0aXZlIHx8IHN0ZXBzLmxlbmd0aCA9PT0gMCkge1xuICAgIGNvbnNvbGUubG9nKCfwn46vIE5vdCByZW5kZXJpbmcgLSB0cmFja2luZ0FjdGl2ZTonLCB0cmFja2luZ0FjdGl2ZSwgJ3N0ZXBzOicsIHN0ZXBzLmxlbmd0aCk7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICBjb25zb2xlLmxvZygn8J+OryBSZW5kZXJpbmcgcHJvZ3Jlc3MgdHJhY2tlciB3aXRoIHN0ZXBzOicsIHN0ZXBzKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxuICAgICAgPE9yY2hlc3RyYXRpb25Qcm9ncmVzc1RyYWNrZXJcbiAgICAgICAgc3RlcHM9e3N0ZXBzfVxuICAgICAgICBpc0FjdGl2ZT17dHJhY2tpbmdBY3RpdmV9XG4gICAgICAgIGF1dG9TY3JvbGw9e3RydWV9XG4gICAgICAvPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJPcmNoZXN0cmF0aW9uUHJvZ3Jlc3NUcmFja2VyIiwidXNlT3JjaGVzdHJhdGlvblRyYWNraW5nIiwiT1JDSEVTVFJBVElPTl9TVEVQUyIsIk9yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZSIsImlzQWN0aXZlIiwib25Qcm9ncmVzc0NhbGxiYWNrIiwiY2xhc3NOYW1lIiwiY29uc29sZSIsImxvZyIsInN0ZXBzIiwidHJhY2tpbmdBY3RpdmUiLCJ0cmFja0V2ZW50IiwiY29tcGxldGVTdGVwIiwic3RhcnRUcmFja2luZyIsImNvbXBsZXRlVHJhY2tpbmciLCJyZXNldFRyYWNraW5nIiwibGVuZ3RoIiwicHJvZ3Jlc3NDYWxsYmFjayIsIm9uQ2xhc3NpZmljYXRpb25TdGFydCIsInR5cGUiLCJzdGVwSWQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZGV0YWlscyIsIm9uQ2xhc3NpZmljYXRpb25Db21wbGV0ZSIsInJvbGVzIiwidGhyZXNob2xkIiwiam9pbiIsIm9uUm9sZVNlbGVjdGlvbkNvbXBsZXRlIiwic2VsZWN0ZWRSb2xlcyIsImZpbHRlcmVkUm9sZXMiLCJvbldvcmtmbG93U2VsZWN0aW9uQ29tcGxldGUiLCJ3b3JrZmxvd1R5cGUiLCJyZWFzb25pbmciLCJ0b1VwcGVyQ2FzZSIsIm9uQWdlbnRDcmVhdGlvblN0YXJ0IiwiTVVMVElfUk9MRSIsIkFHRU5UX0NSRUFUSU9OIiwib25BZ2VudENyZWF0aW9uQ29tcGxldGUiLCJhZ2VudHMiLCJvblN1cGVydmlzb3JJbml0U3RhcnQiLCJTVVBFUlZJU09SX0lOSVQiLCJvblN1cGVydmlzb3JJbml0Q29tcGxldGUiLCJzdXBlcnZpc29yUm9sZSIsIm9uVGFza1BsYW5uaW5nU3RhcnQiLCJUQVNLX1BMQU5OSU5HIiwib25UYXNrUGxhbm5pbmdDb21wbGV0ZSIsInBsYW4iLCJvbkFnZW50V29ya1N0YXJ0Iiwicm9sZSIsInRhc2siLCJyb2xlTmFtZXMiLCJvbkFnZW50V29ya0NvbXBsZXRlIiwicmVzdWx0Iiwib25TdXBlcnZpc29yU3ludGhlc2lzU3RhcnQiLCJvblN1cGVydmlzb3JTeW50aGVzaXNDb21wbGV0ZSIsInN5bnRoZXNpcyIsIm9uT3JjaGVzdHJhdGlvbkNvbXBsZXRlIiwib25FcnJvciIsInN0ZXAiLCJlcnJvciIsInNldFRpbWVvdXQiLCJzdGVwc0xlbmd0aCIsInNob3VsZFJlbmRlciIsImRpdiIsImF1dG9TY3JvbGwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});