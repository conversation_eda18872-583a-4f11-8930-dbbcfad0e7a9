"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        className\n    });\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = useOrchestrationTracking();\n    console.log('🎯 Progress state:', {\n        steps: steps.length,\n        trackingActive,\n        isActive\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'classification',\n                    stepId: 'analyzing_request',\n                    title: 'Analyzing request with RouKey AI',\n                    description: 'Understanding your request and requirements...',\n                    details: [\n                        'Processing natural language input',\n                        'Identifying task complexity and scope',\n                        'Determining specialist expertise needed',\n                        'Preparing for role classification'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('analyzing_request', [\n                    \"✅ Request analysis complete\",\n                    \"\\uD83C\\uDFAF Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83D\\uDCCA Classification threshold: \".concat(threshold),\n                    \"\\uD83D\\uDE80 Ready to prepare AI specialists\"\n                ]);\n                // Add detailed role breakdown\n                trackEvent({\n                    type: 'role_selection',\n                    stepId: 'preparing_specialists',\n                    title: 'Preparing AI Specialists',\n                    description: 'Setting up the perfect expert team for your task...',\n                    details: [\n                        \"Selected roles: \".concat(roles.join(', ')),\n                        'Configuring specialist capabilities',\n                        'Assigning API keys to each role',\n                        'Preparing collaborative workflow'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                completeStep('preparing_specialists', [\n                    \"✅ Specialist team configured\",\n                    \"\\uD83D\\uDC65 \".concat(selectedRoles.length, \" experts selected: \").concat(selectedRoles.join(', ')),\n                    \"\\uD83D\\uDD27 API keys assigned and validated\",\n                    \"⚡ Ready for workflow orchestration\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                completeStep('orchestration_planning', [\n                    \"✅ Orchestration strategy finalized\",\n                    \"\\uD83C\\uDFAF Selected approach: \".concat(workflowType.toUpperCase()),\n                    \"\\uD83D\\uDCA1 Strategy: \".concat(reasoning),\n                    \"\\uD83D\\uDE80 Ready to create AI agents\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'agent_creation',\n                    stepId: 'creating_agents',\n                    title: 'Creating AI Agents',\n                    description: 'Initializing specialized AI agents with your API keys...',\n                    details: [\n                        'Configuring agent personalities and capabilities',\n                        'Assigning API keys to each specialist',\n                        'Setting up communication protocols',\n                        'Preparing for collaborative work'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('creating_agents', [\n                    \"✅ \".concat(agents.length, \" AI agents created successfully\"),\n                    \"\\uD83D\\uDD11 API keys: \".concat(agents.map({\n                        \"OrchestrationProgressBridge.useCallback[progressCallback]\": (a)=>\"\".concat(a.role, \" (\").concat(a.apiKey, \")\")\n                    }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]).join(', ')),\n                    \"\\uD83E\\uDD16 All agents ready for collaboration\",\n                    \"⚡ Team assembly complete\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_init',\n                    stepId: 'supervisor_setup',\n                    title: 'Setting Up Team Coordinator',\n                    description: 'Initializing supervisor agent to manage collaboration...',\n                    details: [\n                        'Selecting most capable agent as supervisor',\n                        'Establishing communication protocols',\n                        'Setting up task delegation system',\n                        'Preparing coordination strategies'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_setup', [\n                    \"✅ Team coordinator ready: \".concat(supervisorRole),\n                    \"\\uD83D\\uDCCB Communication channels established\",\n                    \"\\uD83C\\uDFAF Task delegation system active\",\n                    \"⚡ Ready to manage collaborative workflow\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'task_planning',\n                    stepId: 'task_planning',\n                    title: 'Planning Task Distribution',\n                    description: 'Supervisor is planning how to distribute work among specialists...',\n                    details: [\n                        'Analyzing task complexity and requirements',\n                        'Determining optimal work distribution',\n                        'Planning collaboration sequence',\n                        'Preparing task assignments for each agent'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    \"✅ Work distribution strategy finalized\",\n                    \"\\uD83D\\uDCCB Plan: \".concat(plan),\n                    \"\\uD83D\\uDC65 Each specialist knows their responsibilities\",\n                    \"\\uD83D\\uDE80 Team ready to begin collaborative work\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    \"✅ Final response synthesis completed\",\n                    \"\\uD83C\\uDFAF All specialist insights successfully combined\",\n                    \"\\uD83D\\uDCDD Comprehensive response ready\",\n                    \"⚡ Collaborative work finished successfully\"\n                ]);\n                // Add final \"generating response\" step\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        trackEvent({\n                            type: 'orchestration_complete',\n                            stepId: 'generating_response',\n                            title: 'Generating Response',\n                            description: 'Finalizing and streaming your comprehensive response...',\n                            details: [\n                                'Formatting final response',\n                                'Preparing for streaming delivery',\n                                'Quality assurance complete',\n                                'Ready to deliver results'\n                            ]\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 200);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                var _result_metadata, _result_metadata1;\n                completeStep('generating_response', [\n                    \"✅ Response generation complete\",\n                    \"\\uD83D\\uDCCA Total tokens: \".concat(((_result_metadata = result.metadata) === null || _result_metadata === void 0 ? void 0 : _result_metadata.totalTokens) || 'N/A'),\n                    \"⏱️ Execution time: \".concat(((_result_metadata1 = result.metadata) === null || _result_metadata1 === void 0 ? void 0 : _result_metadata1.executionTime) || 'N/A', \"ms\"),\n                    \"\\uD83C\\uDF89 Multi-role orchestration successful!\"\n                ]);\n                // Complete tracking after a short delay to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        completeTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 2000);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", true);\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});