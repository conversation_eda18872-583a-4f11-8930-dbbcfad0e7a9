"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationTracking */ \"(app-pages-browser)/./src/hooks/useOrchestrationTracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        className\n    });\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = (0,_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking)();\n    console.log('🎯 Progress state:', {\n        steps: steps.length,\n        trackingActive,\n        isActive\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'classification',\n                    stepId: 'analyzing_request',\n                    title: 'Analyzing request with RouKey AI',\n                    description: 'Understanding your request and requirements...',\n                    details: [\n                        'Processing natural language input',\n                        'Identifying task complexity and scope',\n                        'Determining specialist expertise needed',\n                        'Preparing for role classification'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('analyzing_request', [\n                    \"✅ Request analysis complete\",\n                    \"\\uD83C\\uDFAF Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83D\\uDCCA Classification threshold: \".concat(threshold),\n                    \"\\uD83D\\uDE80 Ready to prepare AI specialists\"\n                ]);\n                // Add detailed role breakdown\n                trackEvent({\n                    type: 'role_selection',\n                    stepId: 'preparing_specialists',\n                    title: 'Preparing AI Specialists',\n                    description: 'Setting up the perfect expert team for your task...',\n                    details: [\n                        \"Selected roles: \".concat(roles.join(', ')),\n                        'Configuring specialist capabilities',\n                        'Assigning API keys to each role',\n                        'Preparing collaborative workflow'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                completeStep('preparing_specialists', [\n                    \"✅ Specialist team configured\",\n                    \"\\uD83D\\uDC65 \".concat(selectedRoles.length, \" experts selected: \").concat(selectedRoles.join(', ')),\n                    \"\\uD83D\\uDD27 API keys assigned and validated\",\n                    \"⚡ Ready for workflow orchestration\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                completeStep('orchestration_planning', [\n                    \"✅ Orchestration strategy finalized\",\n                    \"\\uD83C\\uDFAF Selected approach: \".concat(workflowType.toUpperCase()),\n                    \"\\uD83D\\uDCA1 Strategy: \".concat(reasoning),\n                    \"\\uD83D\\uDE80 Ready to create AI agents\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'agent_creation',\n                    stepId: 'creating_agents',\n                    title: 'Creating AI Agents',\n                    description: 'Initializing specialized AI agents with your API keys...',\n                    details: [\n                        'Configuring agent personalities and capabilities',\n                        'Assigning API keys to each specialist',\n                        'Setting up communication protocols',\n                        'Preparing for collaborative work'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('creating_agents', [\n                    \"✅ \".concat(agents.length, \" AI agents created successfully\"),\n                    \"\\uD83D\\uDD11 API keys: \".concat(agents.map({\n                        \"OrchestrationProgressBridge.useCallback[progressCallback]\": (a)=>\"\".concat(a.role, \" (\").concat(a.apiKey, \")\")\n                    }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]).join(', ')),\n                    \"\\uD83E\\uDD16 All agents ready for collaboration\",\n                    \"⚡ Team assembly complete\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_init',\n                    stepId: 'supervisor_setup',\n                    title: 'Setting Up Team Coordinator',\n                    description: 'Initializing supervisor agent to manage collaboration...',\n                    details: [\n                        'Selecting most capable agent as supervisor',\n                        'Establishing communication protocols',\n                        'Setting up task delegation system',\n                        'Preparing coordination strategies'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_setup', [\n                    \"✅ Team coordinator ready: \".concat(supervisorRole),\n                    \"\\uD83D\\uDCCB Communication channels established\",\n                    \"\\uD83C\\uDFAF Task delegation system active\",\n                    \"⚡ Ready to manage collaborative workflow\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'task_planning',\n                    stepId: 'task_planning',\n                    title: 'Planning Task Distribution',\n                    description: 'Supervisor is planning how to distribute work among specialists...',\n                    details: [\n                        'Analyzing task complexity and requirements',\n                        'Determining optimal work distribution',\n                        'Planning collaboration sequence',\n                        'Preparing task assignments for each agent'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    \"✅ Work distribution strategy finalized\",\n                    \"\\uD83D\\uDCCB Plan: \".concat(plan),\n                    \"\\uD83D\\uDC65 Each specialist knows their responsibilities\",\n                    \"\\uD83D\\uDE80 Team ready to begin collaborative work\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    'Final response synthesis completed',\n                    'All specialist insights successfully combined',\n                    'Collaborative work finished successfully'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                completeTracking();\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 272,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", false, function() {\n    return [\n        _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});