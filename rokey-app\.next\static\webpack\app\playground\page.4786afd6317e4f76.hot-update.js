"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationSSE */ \"(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, sessionId, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        sessionId,\n        className\n    });\n    // Use SSE-based progress tracking\n    const { steps, isActive: sseActive, isComplete, error, connect, disconnect, reset } = (0,_hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE)(sessionId);\n    console.log('🎯 SSE Progress state:', {\n        steps: steps.length,\n        sseActive,\n        isActive,\n        sessionId,\n        isComplete,\n        error\n    });\n    // Create a dummy progress callback for backward compatibility\n    // The real progress tracking now happens via SSE\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Classification started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                console.log('[Progress Bridge] Classification complete (SSE mode):', roles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                console.log('[Progress Bridge] Role selection complete (SSE mode):', selectedRoles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                console.log('[Progress Bridge] Workflow selection complete (SSE mode):', workflowType);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Agent creation started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                console.log('[Progress Bridge] Agent creation complete (SSE mode):', agents.length);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_init',\n                    stepId: 'supervisor_setup',\n                    title: 'Setting Up Team Coordinator',\n                    description: 'Initializing supervisor agent to manage collaboration...',\n                    details: [\n                        'Selecting most capable agent as supervisor',\n                        'Establishing communication protocols',\n                        'Setting up task delegation system',\n                        'Preparing coordination strategies'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_setup', [\n                    \"✅ Team coordinator ready: \".concat(supervisorRole),\n                    \"\\uD83D\\uDCCB Communication channels established\",\n                    \"\\uD83C\\uDFAF Task delegation system active\",\n                    \"⚡ Ready to manage collaborative workflow\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'task_planning',\n                    stepId: 'task_planning',\n                    title: 'Planning Task Distribution',\n                    description: 'Supervisor is planning how to distribute work among specialists...',\n                    details: [\n                        'Analyzing task complexity and requirements',\n                        'Determining optimal work distribution',\n                        'Planning collaboration sequence',\n                        'Preparing task assignments for each agent'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    \"✅ Work distribution strategy finalized\",\n                    \"\\uD83D\\uDCCB Plan: \".concat(plan),\n                    \"\\uD83D\\uDC65 Each specialist knows their responsibilities\",\n                    \"\\uD83D\\uDE80 Team ready to begin collaborative work\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    \"✅ Final response synthesis completed\",\n                    \"\\uD83C\\uDFAF All specialist insights successfully combined\",\n                    \"\\uD83D\\uDCDD Comprehensive response ready\",\n                    \"⚡ Collaborative work finished successfully\"\n                ]);\n                // Add final \"generating response\" step\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        trackEvent({\n                            type: 'orchestration_complete',\n                            stepId: 'generating_response',\n                            title: 'Generating Response',\n                            description: 'Finalizing and streaming your comprehensive response...',\n                            details: [\n                                'Formatting final response',\n                                'Preparing for streaming delivery',\n                                'Quality assurance complete',\n                                'Ready to deliver results'\n                            ]\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 200);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                var _result_metadata, _result_metadata1;\n                completeStep('generating_response', [\n                    \"✅ Response generation complete\",\n                    \"\\uD83D\\uDCCA Total tokens: \".concat(((_result_metadata = result.metadata) === null || _result_metadata === void 0 ? void 0 : _result_metadata.totalTokens) || 'N/A'),\n                    \"⏱️ Execution time: \".concat(((_result_metadata1 = result.metadata) === null || _result_metadata1 === void 0 ? void 0 : _result_metadata1.executionTime) || 'N/A', \"ms\"),\n                    \"\\uD83C\\uDF89 Multi-role orchestration successful!\"\n                ]);\n                // Complete tracking after a short delay to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        completeTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 2000);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 254,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"MV7b4B3Euz5CPsltLA582nV5DWw=\", false, function() {\n    return [\n        _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});