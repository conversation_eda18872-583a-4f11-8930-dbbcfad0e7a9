"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationTracking */ \"(app-pages-browser)/./src/hooks/useOrchestrationTracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        className\n    });\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = (0,_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking)();\n    console.log('🎯 Progress state:', {\n        steps: steps.length,\n        trackingActive,\n        isActive\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.CLASSIFICATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('classification', [\n                    \"Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"Analysis completed successfully\",\n                    \"Ready to select the best experts\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.ROLE_SELECTION,\n                    details: [\n                        \"Selected \".concat(selectedRoles.length, \" AI specialists\"),\n                        \"Each expert has been assigned their optimal tools\",\n                        \"Team assembly complete\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('role_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                const workflowNames = {\n                    'sequential': 'Step-by-step collaboration',\n                    'supervisor': 'Coordinated teamwork',\n                    'hierarchical': 'Multi-level coordination',\n                    'parallel': 'Simultaneous processing'\n                };\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.WORKFLOW_SELECTION,\n                    description: \"\".concat(workflowNames[workflowType] || 'Smart collaboration', \" approach selected\"),\n                    details: [\n                        \"Collaboration strategy: \".concat(workflowNames[workflowType] || workflowType),\n                        \"Optimized for your specific request type\",\n                        \"Team coordination plan established\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('workflow_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('agent_creation', [\n                    \"\".concat(agents.length, \" AI specialists ready to work\"),\n                    \"Each expert configured with optimal settings\",\n                    \"Team assembly complete and ready to collaborate\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_init', [\n                    'Team coordinator assigned and ready',\n                    'Communication channels established',\n                    'Ready to manage collaborative workflow'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    'Work distribution strategy finalized',\n                    'Each specialist knows their role and responsibilities',\n                    'Team ready to begin collaborative work'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    'Final response synthesis completed',\n                    'All specialist insights successfully combined',\n                    'Collaborative work finished successfully'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                completeTracking();\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n                // For testing: simulate a simple progress flow\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'classification',\n                            stepId: 'classification',\n                            title: 'Analyzing request with RouKey AI',\n                            description: 'Understanding your request and requirements...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('classification', [\n                            'Request analysis completed',\n                            'Optimal approach identified',\n                            'Ready to process your request'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 1500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'agent_creation',\n                            stepId: 'agent_creation',\n                            title: 'Preparing AI Specialist',\n                            description: 'Setting up the perfect expert for your task...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 2000);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('agent_creation', [\n                            'AI specialist ready to work',\n                            'Expert configured with optimal settings',\n                            'Processing your request...'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking,\n        trackEvent,\n        completeStep\n    ]);\n    if (!trackingActive || steps.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", false, function() {\n    return [\n        _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});