"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationSSE */ \"(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, sessionId, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        sessionId,\n        className\n    });\n    // Use SSE-based progress tracking\n    const { steps, isActive: sseActive, isComplete, error, connect, disconnect, reset } = (0,_hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE)(sessionId);\n    console.log('🎯 SSE Progress state:', {\n        steps: steps.length,\n        sseActive,\n        isActive,\n        sessionId,\n        isComplete,\n        error\n    });\n    // Create a dummy progress callback for backward compatibility\n    // The real progress tracking now happens via SSE\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Classification started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                console.log('[Progress Bridge] Classification complete (SSE mode):', roles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                console.log('[Progress Bridge] Role selection complete (SSE mode):', selectedRoles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                console.log('[Progress Bridge] Workflow selection complete (SSE mode):', workflowType);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Agent creation started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                console.log('[Progress Bridge] Agent creation complete (SSE mode):', agents.length);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Supervisor init started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                console.log('[Progress Bridge] Supervisor init complete (SSE mode):', supervisorRole);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Task planning started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                console.log('[Progress Bridge] Task planning complete (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                console.log('[Progress Bridge] Agent work started (SSE mode):', role);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                console.log('[Progress Bridge] Agent work complete (SSE mode):', role);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Supervisor synthesis started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                console.log('[Progress Bridge] Supervisor synthesis complete (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                console.log('[Progress Bridge] Orchestration complete (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                console.log('[Progress Bridge] Error (SSE mode):', step, error);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, []);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"MV7b4B3Euz5CPsltLA582nV5DWw=\", false, function() {\n    return [\n        _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFc0Q7QUFDb0I7QUFDUjtBQVVuRCxTQUFTSyw0QkFBNEIsS0FLakI7UUFMaUIsRUFDbERDLFFBQVEsRUFDUkMsU0FBUyxFQUNUQyxrQkFBa0IsRUFDbEJDLFlBQVksRUFBRSxFQUNtQixHQUxpQjs7SUFNbERDLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEM7UUFBRUw7UUFBVUM7UUFBV0U7SUFBVTtJQUV2RixrQ0FBa0M7SUFDbEMsTUFBTSxFQUNKRyxLQUFLLEVBQ0xOLFVBQVVPLFNBQVMsRUFDbkJDLFVBQVUsRUFDVkMsS0FBSyxFQUNMQyxPQUFPLEVBQ1BDLFVBQVUsRUFDVkMsS0FBSyxFQUNOLEdBQUdkLCtFQUFtQkEsQ0FBQ0c7SUFFeEJHLFFBQVFDLEdBQUcsQ0FBQywwQkFBMEI7UUFDcENDLE9BQU9BLE1BQU1PLE1BQU07UUFDbkJOO1FBQ0FQO1FBQ0FDO1FBQ0FPO1FBQ0FDO0lBQ0Y7SUFFQSw4REFBOEQ7SUFDOUQsaURBQWlEO0lBQ2pELE1BQU1LLG1CQUFxQ2xCLGtEQUFXQSxDQUFDO1FBQ3JEbUIscUJBQXFCO3lFQUFFO2dCQUNyQlgsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7O1FBRUFXLHdCQUF3Qjt5RUFBRSxDQUFDQyxPQUFpQkM7Z0JBQzFDZCxRQUFRQyxHQUFHLENBQUMseURBQXlEWTtZQUN2RTs7UUFFQUUsdUJBQXVCO3lFQUFFLENBQUNDLGVBQXlCQztnQkFDakRqQixRQUFRQyxHQUFHLENBQUMseURBQXlEZTtZQUN2RTs7UUFFQUUsMkJBQTJCO3lFQUFFLENBQUNDLGNBQXNCQztnQkFDbERwQixRQUFRQyxHQUFHLENBQUMsNkRBQTZEa0I7WUFDM0U7O1FBRUFFLG9CQUFvQjt5RUFBRTtnQkFDcEJyQixRQUFRQyxHQUFHLENBQUM7WUFDZDs7UUFFQXFCLHVCQUF1Qjt5RUFBRSxDQUFDQztnQkFDeEJ2QixRQUFRQyxHQUFHLENBQUMseURBQXlEc0IsT0FBT2QsTUFBTTtZQUNwRjs7UUFFQWUscUJBQXFCO3lFQUFFO2dCQUNyQnhCLFFBQVFDLEdBQUcsQ0FBQztZQUNkOztRQUVBd0Isd0JBQXdCO3lFQUFFLENBQUNDO2dCQUN6QjFCLFFBQVFDLEdBQUcsQ0FBQywwREFBMER5QjtZQUN4RTs7UUFFQUMsbUJBQW1CO3lFQUFFO2dCQUNuQjNCLFFBQVFDLEdBQUcsQ0FBQztZQUNkOztRQUVBMkIsc0JBQXNCO3lFQUFFLENBQUNDO2dCQUN2QjdCLFFBQVFDLEdBQUcsQ0FBQztZQUNkOztRQUVBNkIsZ0JBQWdCO3lFQUFFLENBQUNDLE1BQWNDO2dCQUMvQmhDLFFBQVFDLEdBQUcsQ0FBQyxvREFBb0Q4QjtZQUNsRTs7UUFFQUUsbUJBQW1CO3lFQUFFLENBQUNGLE1BQWNHO2dCQUNsQ2xDLFFBQVFDLEdBQUcsQ0FBQyxxREFBcUQ4QjtZQUNuRTs7UUFFQUksMEJBQTBCO3lFQUFFO2dCQUMxQm5DLFFBQVFDLEdBQUcsQ0FBQztZQUNkOztRQUVBbUMsNkJBQTZCO3lFQUFFLENBQUNDO2dCQUM5QnJDLFFBQVFDLEdBQUcsQ0FBQztZQUNkOztRQUVBcUMsdUJBQXVCO3lFQUFFLENBQUNKO2dCQUN4QmxDLFFBQVFDLEdBQUcsQ0FBQztZQUNkOztRQUVBc0MsT0FBTzt5RUFBRSxDQUFDQyxNQUFjbkM7Z0JBQ3RCTCxRQUFRQyxHQUFHLENBQUMsdUNBQXVDdUMsTUFBTW5DO1lBQzNEOztJQUNGLEdBQUcsRUFBRTtJQUVMLHNDQUFzQztJQUN0Q2QsZ0RBQVNBO2lEQUFDO1lBQ1IsSUFBSU8sb0JBQW9CO2dCQUN0QkEsbUJBQW1CWTtZQUNyQjtRQUNGO2dEQUFHO1FBQUNaO1FBQW9CWTtLQUFpQjtJQUV6Qyx3Q0FBd0M7SUFDeENuQixnREFBU0E7aURBQUM7WUFDUixJQUFJSyxZQUFZLENBQUM2QyxnQkFBZ0I7Z0JBQy9CQztZQUNGLE9BQU8sSUFBSSxDQUFDOUMsWUFBWTZDLGdCQUFnQjtnQkFDdEMsbURBQW1EO2dCQUNuREU7NkRBQVc7d0JBQ1RDO29CQUNGOzREQUFHO1lBQ0w7UUFDRjtnREFBRztRQUFDaEQ7UUFBVTZDO1FBQWdCQztRQUFlRTtLQUFjO0lBRTNENUMsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QjtRQUFFd0M7UUFBZ0JJLGFBQWEzQyxNQUFNTyxNQUFNO1FBQUVxQyxjQUFjTCxrQkFBa0J2QyxNQUFNTyxNQUFNLEdBQUc7SUFBRTtJQUVqSSxJQUFJLENBQUNnQyxrQkFBa0J2QyxNQUFNTyxNQUFNLEtBQUssR0FBRztRQUN6Q1QsUUFBUUMsR0FBRyxDQUFDLHNDQUFzQ3dDLGdCQUFnQixVQUFVdkMsTUFBTU8sTUFBTTtRQUN4RixPQUFPO0lBQ1Q7SUFFQVQsUUFBUUMsR0FBRyxDQUFDLDZDQUE2Q0M7SUFFekQscUJBQ0UsOERBQUM2QztRQUFJaEQsV0FBV0E7a0JBQ2QsNEVBQUNOLHFFQUE0QkE7WUFDM0JTLE9BQU9BO1lBQ1BOLFVBQVU2QztZQUNWTyxZQUFZOzs7Ozs7Ozs7OztBQUlwQjtHQXJJd0JyRDs7UUFpQmxCRCwyRUFBbUJBOzs7S0FqQkRDIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcT3JjaGVzdHJhdGlvblByb2dyZXNzQnJpZGdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IE9yY2hlc3RyYXRpb25Qcm9ncmVzc1RyYWNrZXIgZnJvbSAnLi9PcmNoZXN0cmF0aW9uUHJvZ3Jlc3NUcmFja2VyJztcbmltcG9ydCB7IHVzZU9yY2hlc3RyYXRpb25TU0UgfSBmcm9tICdAL2hvb2tzL3VzZU9yY2hlc3RyYXRpb25TU0UnO1xuaW1wb3J0IHsgdHlwZSBQcm9ncmVzc0NhbGxiYWNrIH0gZnJvbSAnQC9saWIvbGFuZ2dyYXBoLW9yY2hlc3RyYXRpb24vUm91S2V5TGFuZ0dyYXBoSW50ZWdyYXRpb24nO1xuXG5pbnRlcmZhY2UgT3JjaGVzdHJhdGlvblByb2dyZXNzQnJpZGdlUHJvcHMge1xuICBpc0FjdGl2ZTogYm9vbGVhbjtcbiAgc2Vzc2lvbklkPzogc3RyaW5nIHwgbnVsbDtcbiAgb25Qcm9ncmVzc0NhbGxiYWNrPzogKGNhbGxiYWNrOiBQcm9ncmVzc0NhbGxiYWNrKSA9PiB2b2lkO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE9yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZSh7XG4gIGlzQWN0aXZlLFxuICBzZXNzaW9uSWQsXG4gIG9uUHJvZ3Jlc3NDYWxsYmFjayxcbiAgY2xhc3NOYW1lID0gJydcbn06IE9yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZVByb3BzKSB7XG4gIGNvbnNvbGUubG9nKCfwn46vIE9yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZSByZW5kZXI6JywgeyBpc0FjdGl2ZSwgc2Vzc2lvbklkLCBjbGFzc05hbWUgfSk7XG5cbiAgLy8gVXNlIFNTRS1iYXNlZCBwcm9ncmVzcyB0cmFja2luZ1xuICBjb25zdCB7XG4gICAgc3RlcHMsXG4gICAgaXNBY3RpdmU6IHNzZUFjdGl2ZSxcbiAgICBpc0NvbXBsZXRlLFxuICAgIGVycm9yLFxuICAgIGNvbm5lY3QsXG4gICAgZGlzY29ubmVjdCxcbiAgICByZXNldFxuICB9ID0gdXNlT3JjaGVzdHJhdGlvblNTRShzZXNzaW9uSWQpO1xuXG4gIGNvbnNvbGUubG9nKCfwn46vIFNTRSBQcm9ncmVzcyBzdGF0ZTonLCB7XG4gICAgc3RlcHM6IHN0ZXBzLmxlbmd0aCxcbiAgICBzc2VBY3RpdmUsXG4gICAgaXNBY3RpdmUsXG4gICAgc2Vzc2lvbklkLFxuICAgIGlzQ29tcGxldGUsXG4gICAgZXJyb3JcbiAgfSk7XG5cbiAgLy8gQ3JlYXRlIGEgZHVtbXkgcHJvZ3Jlc3MgY2FsbGJhY2sgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcbiAgLy8gVGhlIHJlYWwgcHJvZ3Jlc3MgdHJhY2tpbmcgbm93IGhhcHBlbnMgdmlhIFNTRVxuICBjb25zdCBwcm9ncmVzc0NhbGxiYWNrOiBQcm9ncmVzc0NhbGxiYWNrID0gdXNlQ2FsbGJhY2soe1xuICAgIG9uQ2xhc3NpZmljYXRpb25TdGFydDogKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1tQcm9ncmVzcyBCcmlkZ2VdIENsYXNzaWZpY2F0aW9uIHN0YXJ0ZWQgKFNTRSBtb2RlKScpO1xuICAgIH0sXG5cbiAgICBvbkNsYXNzaWZpY2F0aW9uQ29tcGxldGU6IChyb2xlczogc3RyaW5nW10sIHRocmVzaG9sZDogbnVtYmVyKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnW1Byb2dyZXNzIEJyaWRnZV0gQ2xhc3NpZmljYXRpb24gY29tcGxldGUgKFNTRSBtb2RlKTonLCByb2xlcyk7XG4gICAgfSxcblxuICAgIG9uUm9sZVNlbGVjdGlvbkNvbXBsZXRlOiAoc2VsZWN0ZWRSb2xlczogc3RyaW5nW10sIGZpbHRlcmVkUm9sZXM6IHN0cmluZ1tdKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnW1Byb2dyZXNzIEJyaWRnZV0gUm9sZSBzZWxlY3Rpb24gY29tcGxldGUgKFNTRSBtb2RlKTonLCBzZWxlY3RlZFJvbGVzKTtcbiAgICB9LFxuXG4gICAgb25Xb3JrZmxvd1NlbGVjdGlvbkNvbXBsZXRlOiAod29ya2Zsb3dUeXBlOiBzdHJpbmcsIHJlYXNvbmluZzogc3RyaW5nKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnW1Byb2dyZXNzIEJyaWRnZV0gV29ya2Zsb3cgc2VsZWN0aW9uIGNvbXBsZXRlIChTU0UgbW9kZSk6Jywgd29ya2Zsb3dUeXBlKTtcbiAgICB9LFxuXG4gICAgb25BZ2VudENyZWF0aW9uU3RhcnQ6ICgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdbUHJvZ3Jlc3MgQnJpZGdlXSBBZ2VudCBjcmVhdGlvbiBzdGFydGVkIChTU0UgbW9kZSknKTtcbiAgICB9LFxuXG4gICAgb25BZ2VudENyZWF0aW9uQ29tcGxldGU6IChhZ2VudHM6IEFycmF5PHsgcm9sZTogc3RyaW5nLCBhcGlLZXk6IHN0cmluZyB9PikgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1tQcm9ncmVzcyBCcmlkZ2VdIEFnZW50IGNyZWF0aW9uIGNvbXBsZXRlIChTU0UgbW9kZSk6JywgYWdlbnRzLmxlbmd0aCk7XG4gICAgfSxcblxuICAgIG9uU3VwZXJ2aXNvckluaXRTdGFydDogKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1tQcm9ncmVzcyBCcmlkZ2VdIFN1cGVydmlzb3IgaW5pdCBzdGFydGVkIChTU0UgbW9kZSknKTtcbiAgICB9LFxuXG4gICAgb25TdXBlcnZpc29ySW5pdENvbXBsZXRlOiAoc3VwZXJ2aXNvclJvbGU6IHN0cmluZykgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1tQcm9ncmVzcyBCcmlkZ2VdIFN1cGVydmlzb3IgaW5pdCBjb21wbGV0ZSAoU1NFIG1vZGUpOicsIHN1cGVydmlzb3JSb2xlKTtcbiAgICB9LFxuXG4gICAgb25UYXNrUGxhbm5pbmdTdGFydDogKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1tQcm9ncmVzcyBCcmlkZ2VdIFRhc2sgcGxhbm5pbmcgc3RhcnRlZCAoU1NFIG1vZGUpJyk7XG4gICAgfSxcblxuICAgIG9uVGFza1BsYW5uaW5nQ29tcGxldGU6IChwbGFuOiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdbUHJvZ3Jlc3MgQnJpZGdlXSBUYXNrIHBsYW5uaW5nIGNvbXBsZXRlIChTU0UgbW9kZSknKTtcbiAgICB9LFxuXG4gICAgb25BZ2VudFdvcmtTdGFydDogKHJvbGU6IHN0cmluZywgdGFzazogc3RyaW5nKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnW1Byb2dyZXNzIEJyaWRnZV0gQWdlbnQgd29yayBzdGFydGVkIChTU0UgbW9kZSk6Jywgcm9sZSk7XG4gICAgfSxcblxuICAgIG9uQWdlbnRXb3JrQ29tcGxldGU6IChyb2xlOiBzdHJpbmcsIHJlc3VsdDogc3RyaW5nKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnW1Byb2dyZXNzIEJyaWRnZV0gQWdlbnQgd29yayBjb21wbGV0ZSAoU1NFIG1vZGUpOicsIHJvbGUpO1xuICAgIH0sXG5cbiAgICBvblN1cGVydmlzb3JTeW50aGVzaXNTdGFydDogKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1tQcm9ncmVzcyBCcmlkZ2VdIFN1cGVydmlzb3Igc3ludGhlc2lzIHN0YXJ0ZWQgKFNTRSBtb2RlKScpO1xuICAgIH0sXG5cbiAgICBvblN1cGVydmlzb3JTeW50aGVzaXNDb21wbGV0ZTogKHN5bnRoZXNpczogc3RyaW5nKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnW1Byb2dyZXNzIEJyaWRnZV0gU3VwZXJ2aXNvciBzeW50aGVzaXMgY29tcGxldGUgKFNTRSBtb2RlKScpO1xuICAgIH0sXG5cbiAgICBvbk9yY2hlc3RyYXRpb25Db21wbGV0ZTogKHJlc3VsdDogYW55KSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnW1Byb2dyZXNzIEJyaWRnZV0gT3JjaGVzdHJhdGlvbiBjb21wbGV0ZSAoU1NFIG1vZGUpJyk7XG4gICAgfSxcblxuICAgIG9uRXJyb3I6IChzdGVwOiBzdHJpbmcsIGVycm9yOiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdbUHJvZ3Jlc3MgQnJpZGdlXSBFcnJvciAoU1NFIG1vZGUpOicsIHN0ZXAsIGVycm9yKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvLyBQcm92aWRlIHByb2dyZXNzIGNhbGxiYWNrIHRvIHBhcmVudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChvblByb2dyZXNzQ2FsbGJhY2spIHtcbiAgICAgIG9uUHJvZ3Jlc3NDYWxsYmFjayhwcm9ncmVzc0NhbGxiYWNrKTtcbiAgICB9XG4gIH0sIFtvblByb2dyZXNzQ2FsbGJhY2ssIHByb2dyZXNzQ2FsbGJhY2tdKTtcblxuICAvLyBTdGFydC9zdG9wIHRyYWNraW5nIGJhc2VkIG9uIGlzQWN0aXZlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzQWN0aXZlICYmICF0cmFja2luZ0FjdGl2ZSkge1xuICAgICAgc3RhcnRUcmFja2luZygpO1xuICAgIH0gZWxzZSBpZiAoIWlzQWN0aXZlICYmIHRyYWNraW5nQWN0aXZlKSB7XG4gICAgICAvLyBTbWFsbCBkZWxheSBiZWZvcmUgcmVzZXR0aW5nIHRvIHNob3cgZmluYWwgc3RhdGVcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICByZXNldFRyYWNraW5nKCk7XG4gICAgICB9LCAzMDAwKTtcbiAgICB9XG4gIH0sIFtpc0FjdGl2ZSwgdHJhY2tpbmdBY3RpdmUsIHN0YXJ0VHJhY2tpbmcsIHJlc2V0VHJhY2tpbmddKTtcblxuICBjb25zb2xlLmxvZygn8J+OryBSZW5kZXIgZGVjaXNpb246JywgeyB0cmFja2luZ0FjdGl2ZSwgc3RlcHNMZW5ndGg6IHN0ZXBzLmxlbmd0aCwgc2hvdWxkUmVuZGVyOiB0cmFja2luZ0FjdGl2ZSAmJiBzdGVwcy5sZW5ndGggPiAwIH0pO1xuXG4gIGlmICghdHJhY2tpbmdBY3RpdmUgfHwgc3RlcHMubGVuZ3RoID09PSAwKSB7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8gTm90IHJlbmRlcmluZyAtIHRyYWNraW5nQWN0aXZlOicsIHRyYWNraW5nQWN0aXZlLCAnc3RlcHM6Jywgc3RlcHMubGVuZ3RoKTtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIGNvbnNvbGUubG9nKCfwn46vIFJlbmRlcmluZyBwcm9ncmVzcyB0cmFja2VyIHdpdGggc3RlcHM6Jywgc3RlcHMpO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2NsYXNzTmFtZX0+XG4gICAgICA8T3JjaGVzdHJhdGlvblByb2dyZXNzVHJhY2tlclxuICAgICAgICBzdGVwcz17c3RlcHN9XG4gICAgICAgIGlzQWN0aXZlPXt0cmFja2luZ0FjdGl2ZX1cbiAgICAgICAgYXV0b1Njcm9sbD17dHJ1ZX1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIk9yY2hlc3RyYXRpb25Qcm9ncmVzc1RyYWNrZXIiLCJ1c2VPcmNoZXN0cmF0aW9uU1NFIiwiT3JjaGVzdHJhdGlvblByb2dyZXNzQnJpZGdlIiwiaXNBY3RpdmUiLCJzZXNzaW9uSWQiLCJvblByb2dyZXNzQ2FsbGJhY2siLCJjbGFzc05hbWUiLCJjb25zb2xlIiwibG9nIiwic3RlcHMiLCJzc2VBY3RpdmUiLCJpc0NvbXBsZXRlIiwiZXJyb3IiLCJjb25uZWN0IiwiZGlzY29ubmVjdCIsInJlc2V0IiwibGVuZ3RoIiwicHJvZ3Jlc3NDYWxsYmFjayIsIm9uQ2xhc3NpZmljYXRpb25TdGFydCIsIm9uQ2xhc3NpZmljYXRpb25Db21wbGV0ZSIsInJvbGVzIiwidGhyZXNob2xkIiwib25Sb2xlU2VsZWN0aW9uQ29tcGxldGUiLCJzZWxlY3RlZFJvbGVzIiwiZmlsdGVyZWRSb2xlcyIsIm9uV29ya2Zsb3dTZWxlY3Rpb25Db21wbGV0ZSIsIndvcmtmbG93VHlwZSIsInJlYXNvbmluZyIsIm9uQWdlbnRDcmVhdGlvblN0YXJ0Iiwib25BZ2VudENyZWF0aW9uQ29tcGxldGUiLCJhZ2VudHMiLCJvblN1cGVydmlzb3JJbml0U3RhcnQiLCJvblN1cGVydmlzb3JJbml0Q29tcGxldGUiLCJzdXBlcnZpc29yUm9sZSIsIm9uVGFza1BsYW5uaW5nU3RhcnQiLCJvblRhc2tQbGFubmluZ0NvbXBsZXRlIiwicGxhbiIsIm9uQWdlbnRXb3JrU3RhcnQiLCJyb2xlIiwidGFzayIsIm9uQWdlbnRXb3JrQ29tcGxldGUiLCJyZXN1bHQiLCJvblN1cGVydmlzb3JTeW50aGVzaXNTdGFydCIsIm9uU3VwZXJ2aXNvclN5bnRoZXNpc0NvbXBsZXRlIiwic3ludGhlc2lzIiwib25PcmNoZXN0cmF0aW9uQ29tcGxldGUiLCJvbkVycm9yIiwic3RlcCIsInRyYWNraW5nQWN0aXZlIiwic3RhcnRUcmFja2luZyIsInNldFRpbWVvdXQiLCJyZXNldFRyYWNraW5nIiwic3RlcHNMZW5ndGgiLCJzaG91bGRSZW5kZXIiLCJkaXYiLCJhdXRvU2Nyb2xsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});