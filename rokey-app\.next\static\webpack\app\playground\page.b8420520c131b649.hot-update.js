"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationTracking */ \"(app-pages-browser)/./src/hooks/useOrchestrationTracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        className\n    });\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = (0,_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking)();\n    console.log('🎯 Progress state:', {\n        steps: steps.length,\n        trackingActive,\n        isActive\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'classification',\n                    stepId: 'analyzing_request',\n                    title: 'Analyzing request with RouKey AI',\n                    description: 'Understanding your request and requirements...',\n                    details: [\n                        'Processing natural language input',\n                        'Identifying task complexity and scope',\n                        'Determining specialist expertise needed',\n                        'Preparing for role classification'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('analyzing_request', [\n                    \"✅ Request analysis complete\",\n                    \"\\uD83C\\uDFAF Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83D\\uDCCA Classification threshold: \".concat(threshold),\n                    \"\\uD83D\\uDE80 Ready to prepare AI specialists\"\n                ]);\n                // Add detailed role breakdown\n                trackEvent({\n                    type: 'role_selection',\n                    stepId: 'preparing_specialists',\n                    title: 'Preparing AI Specialists',\n                    description: 'Setting up the perfect expert team for your task...',\n                    details: [\n                        \"Selected roles: \".concat(roles.join(', ')),\n                        'Configuring specialist capabilities',\n                        'Assigning API keys to each role',\n                        'Preparing collaborative workflow'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                completeStep('preparing_specialists', [\n                    \"✅ Specialist team configured\",\n                    \"\\uD83D\\uDC65 \".concat(selectedRoles.length, \" experts selected: \").concat(selectedRoles.join(', ')),\n                    \"\\uD83D\\uDD27 API keys assigned and validated\",\n                    \"⚡ Ready for workflow orchestration\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                completeStep('orchestration_planning', [\n                    \"✅ Orchestration strategy finalized\",\n                    \"\\uD83C\\uDFAF Selected approach: \".concat(workflowType.toUpperCase()),\n                    \"\\uD83D\\uDCA1 Strategy: \".concat(reasoning),\n                    \"\\uD83D\\uDE80 Ready to create AI agents\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'agent_creation',\n                    stepId: 'creating_agents',\n                    title: 'Creating AI Agents',\n                    description: 'Initializing specialized AI agents with your API keys...',\n                    details: [\n                        'Configuring agent personalities and capabilities',\n                        'Assigning API keys to each specialist',\n                        'Setting up communication protocols',\n                        'Preparing for collaborative work'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('creating_agents', [\n                    \"✅ \".concat(agents.length, \" AI agents created successfully\"),\n                    \"\\uD83D\\uDD11 API keys: \".concat(agents.map({\n                        \"OrchestrationProgressBridge.useCallback[progressCallback]\": (a)=>\"\".concat(a.role, \" (\").concat(a.apiKey, \")\")\n                    }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]).join(', ')),\n                    \"\\uD83E\\uDD16 All agents ready for collaboration\",\n                    \"⚡ Team assembly complete\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_init',\n                    stepId: 'supervisor_setup',\n                    title: 'Setting Up Team Coordinator',\n                    description: 'Initializing supervisor agent to manage collaboration...',\n                    details: [\n                        'Selecting most capable agent as supervisor',\n                        'Establishing communication protocols',\n                        'Setting up task delegation system',\n                        'Preparing coordination strategies'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_setup', [\n                    \"✅ Team coordinator ready: \".concat(supervisorRole),\n                    \"\\uD83D\\uDCCB Communication channels established\",\n                    \"\\uD83C\\uDFAF Task delegation system active\",\n                    \"⚡ Ready to manage collaborative workflow\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    'Work distribution strategy finalized',\n                    'Each specialist knows their role and responsibilities',\n                    'Team ready to begin collaborative work'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    'Final response synthesis completed',\n                    'All specialist insights successfully combined',\n                    'Collaborative work finished successfully'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                completeTracking();\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n                // For testing: simulate a simple progress flow\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'classification',\n                            stepId: 'classification',\n                            title: 'Analyzing request with RouKey AI',\n                            description: 'Understanding your request and requirements...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('classification', [\n                            'Request analysis completed',\n                            'Optimal approach identified',\n                            'Ready to process your request'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 1500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'agent_creation',\n                            stepId: 'agent_creation',\n                            title: 'Preparing AI Specialist',\n                            description: 'Setting up the perfect expert for your task...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 2000);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('agent_creation', [\n                            'AI specialist ready to work',\n                            'Expert configured with optimal settings',\n                            'Processing your request...'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking,\n        trackEvent,\n        completeStep\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 296,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", false, function() {\n    return [\n        _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFc0Q7QUFDb0I7QUFDdUI7QUFTbEYsU0FBU00sNEJBQTRCLEtBSWpCO1FBSmlCLEVBQ2xEQyxRQUFRLEVBQ1JDLGtCQUFrQixFQUNsQkMsWUFBWSxFQUFFLEVBQ21CLEdBSmlCOztJQUtsREMsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQztRQUFFSjtRQUFVRTtJQUFVO0lBRTVFLE1BQU0sRUFDSkcsS0FBSyxFQUNMTCxVQUFVTSxjQUFjLEVBQ3hCQyxVQUFVLEVBQ1ZDLFlBQVksRUFDWkMsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDaEJDLGFBQWEsRUFDZCxHQUFHZCx5RkFBd0JBO0lBRTVCTSxRQUFRQyxHQUFHLENBQUMsc0JBQXNCO1FBQUVDLE9BQU9BLE1BQU1PLE1BQU07UUFBRU47UUFBZ0JOO0lBQVM7SUFFbEYsb0RBQW9EO0lBQ3BELE1BQU1hLG1CQUFxQ2xCLGtEQUFXQSxDQUFDO1FBQ3JEbUIscUJBQXFCO3lFQUFFO2dCQUNyQlAsV0FBVztvQkFDVFEsTUFBTTtvQkFDTkMsUUFBUTtvQkFDUkMsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUzt3QkFDUDt3QkFDQTt3QkFDQTt3QkFDQTtxQkFDRDtnQkFDSDtZQUNGOztRQUVBQyx3QkFBd0I7eUVBQUUsQ0FBQ0MsT0FBaUJDO2dCQUMxQ2QsYUFBYSxxQkFBcUI7b0JBQy9CO29CQUNBLDJCQUE2QixPQUFiYSxNQUFNVCxNQUFNLEVBQUM7b0JBQzdCLDBDQUF5QyxPQUFWVTtvQkFDL0I7aUJBQ0Y7Z0JBRUQsOEJBQThCO2dCQUM5QmYsV0FBVztvQkFDVFEsTUFBTTtvQkFDTkMsUUFBUTtvQkFDUkMsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUzt3QkFDTixtQkFBbUMsT0FBakJFLE1BQU1FLElBQUksQ0FBQzt3QkFDOUI7d0JBQ0E7d0JBQ0E7cUJBQ0Q7Z0JBQ0g7WUFDRjs7UUFFQUMsdUJBQXVCO3lFQUFFLENBQUNDLGVBQXlCQztnQkFDakRsQixhQUFhLHlCQUF5QjtvQkFDbkM7b0JBQ0EsZ0JBQStDaUIsT0FBMUNBLGNBQWNiLE1BQU0sRUFBQyx1QkFBOEMsT0FBekJhLGNBQWNGLElBQUksQ0FBQztvQkFDbEU7b0JBQ0E7aUJBQ0Y7WUFDSDs7UUFFQUksMkJBQTJCO3lFQUFFLENBQUNDLGNBQXNCQztnQkFDbERyQixhQUFhLDBCQUEwQjtvQkFDcEM7b0JBQ0EsbUNBQW1ELE9BQTNCb0IsYUFBYUUsV0FBVztvQkFDaEQsMEJBQXlCLE9BQVZEO29CQUNmO2lCQUNGO1lBQ0g7O1FBRUFFLG9CQUFvQjt5RUFBRTtnQkFDcEJ4QixXQUFXO29CQUNUUSxNQUFNO29CQUNOQyxRQUFRO29CQUNSQyxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO3dCQUNQO3dCQUNBO3dCQUNBO3dCQUNBO3FCQUNEO2dCQUNIO1lBQ0Y7O1FBRUFhLHVCQUF1Qjt5RUFBRSxDQUFDQztnQkFDeEJ6QixhQUFhLG1CQUFtQjtvQkFDN0IsS0FBa0IsT0FBZHlCLE9BQU9yQixNQUFNLEVBQUM7b0JBQ2xCLDBCQUFxRSxPQUF0RHFCLE9BQU9DLEdBQUc7cUZBQUNDLENBQUFBLElBQUssR0FBY0EsT0FBWEEsRUFBRUMsSUFBSSxFQUFDLE1BQWEsT0FBVEQsRUFBRUUsTUFBTSxFQUFDO29GQUFJZCxJQUFJLENBQUM7b0JBQy9EO29CQUNBO2lCQUNGO1lBQ0g7O1FBRUFlLHFCQUFxQjt5RUFBRTtnQkFDckIvQixXQUFXO29CQUNUUSxNQUFNO29CQUNOQyxRQUFRO29CQUNSQyxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO3dCQUNQO3dCQUNBO3dCQUNBO3dCQUNBO3FCQUNEO2dCQUNIO1lBQ0Y7O1FBRUFvQix3QkFBd0I7eUVBQUUsQ0FBQ0M7Z0JBQ3pCaEMsYUFBYSxvQkFBb0I7b0JBQzlCLDZCQUEyQyxPQUFmZ0M7b0JBQzVCO29CQUNBO29CQUNBO2lCQUNGO1lBQ0g7O1FBRUFDLG1CQUFtQjt5RUFBRTtnQkFDbkJsQyxXQUFXVCxnRkFBbUJBLENBQUM0QyxVQUFVLENBQUNDLGFBQWE7WUFDekQ7O1FBRUFDLHNCQUFzQjt5RUFBRSxDQUFDQztnQkFDdkJyQyxhQUFhLGlCQUFpQjtvQkFDNUI7b0JBQ0E7b0JBQ0E7aUJBQ0Q7WUFDSDs7UUFFQXNDLGdCQUFnQjt5RUFBRSxDQUFDVixNQUFjVztnQkFDL0IsTUFBTUMsWUFBWTtvQkFDaEIsMEJBQTBCO29CQUMxQixXQUFXO29CQUNYLGtCQUFrQjtvQkFDbEIsbUJBQW1CO29CQUNuQixnQkFBZ0I7Z0JBQ2xCO2dCQUVBekMsV0FBVztvQkFDVFEsTUFBTTtvQkFDTkMsUUFBUSxTQUFjLE9BQUxvQjtvQkFDakJuQixPQUFPLEdBQWdFLE9BQTdEK0IsU0FBUyxDQUFDWixLQUErQixJQUFJLGlCQUFnQjtvQkFDdkVsQixhQUFhNkI7b0JBQ2I1QixTQUFTO3dCQUNOLGVBQWdFLE9BQWxENkIsU0FBUyxDQUFDWixLQUErQixJQUFJQTt3QkFDNUQ7d0JBQ0E7cUJBQ0Q7Z0JBQ0g7WUFDRjs7UUFFQWEsbUJBQW1CO3lFQUFFLENBQUNiLE1BQWNjO2dCQUNsQyxNQUFNRixZQUFZO29CQUNoQiwwQkFBMEI7b0JBQzFCLFdBQVc7b0JBQ1gsa0JBQWtCO29CQUNsQixtQkFBbUI7b0JBQ25CLGdCQUFnQjtnQkFDbEI7Z0JBRUF4QyxhQUFhLFNBQWMsT0FBTDRCLE9BQVE7b0JBQzNCLEdBQTRELE9BQTFEWSxTQUFTLENBQUNaLEtBQStCLElBQUksY0FBYTtvQkFDNUQ7b0JBQ0Q7aUJBQ0Q7WUFDSDs7UUFFQWUsMEJBQTBCO3lFQUFFO2dCQUMxQjVDLFdBQVc7b0JBQ1RRLE1BQU07b0JBQ05DLFFBQVE7b0JBQ1JDLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFNBQVM7d0JBQ1A7d0JBQ0E7d0JBQ0E7cUJBQ0Q7Z0JBQ0g7WUFDRjs7UUFFQWlDLDZCQUE2Qjt5RUFBRSxDQUFDQztnQkFDOUI3QyxhQUFhLHdCQUF3QjtvQkFDbkM7b0JBQ0E7b0JBQ0E7aUJBQ0Q7WUFDSDs7UUFFQThDLHVCQUF1Qjt5RUFBRSxDQUFDSjtnQkFDeEJ4QztZQUNGOztRQUVBNkMsT0FBTzt5RUFBRSxDQUFDQyxNQUFjQztnQkFDdEJsRCxXQUFXO29CQUNUUSxNQUFNO29CQUNOQyxRQUFRLFNBQWMsT0FBTHdDO29CQUNqQnZDLE9BQU8sWUFBaUIsT0FBTHVDO29CQUNuQnRDLGFBQWF1QztvQkFDYnRDLFNBQVM7d0JBQ04sU0FBYSxPQUFMcUM7d0JBQ1IsVUFBZSxPQUFOQzt3QkFDVjtxQkFDRDtnQkFDSDtZQUNGOztJQUNGLEdBQUc7UUFBQ2xEO1FBQVlDO1FBQWNFO0tBQWlCO0lBRS9DLHNDQUFzQztJQUN0Q2hCLGdEQUFTQTtpREFBQztZQUNSLElBQUlPLG9CQUFvQjtnQkFDdEJBLG1CQUFtQlk7WUFDckI7UUFDRjtnREFBRztRQUFDWjtRQUFvQlk7S0FBaUI7SUFFekMsd0NBQXdDO0lBQ3hDbkIsZ0RBQVNBO2lEQUFDO1lBQ1IsSUFBSU0sWUFBWSxDQUFDTSxnQkFBZ0I7Z0JBQy9CRztnQkFFQSwrQ0FBK0M7Z0JBQy9DaUQ7NkRBQVc7d0JBQ1RuRCxXQUFXOzRCQUNUUSxNQUFNOzRCQUNOQyxRQUFROzRCQUNSQyxPQUFPOzRCQUNQQyxhQUFhO3dCQUNmO29CQUNGOzREQUFHO2dCQUVId0M7NkRBQVc7d0JBQ1RsRCxhQUFhLGtCQUFrQjs0QkFDN0I7NEJBQ0E7NEJBQ0E7eUJBQ0Q7b0JBQ0g7NERBQUc7Z0JBRUhrRDs2REFBVzt3QkFDVG5ELFdBQVc7NEJBQ1RRLE1BQU07NEJBQ05DLFFBQVE7NEJBQ1JDLE9BQU87NEJBQ1BDLGFBQWE7d0JBQ2Y7b0JBQ0Y7NERBQUc7Z0JBRUh3Qzs2REFBVzt3QkFDVGxELGFBQWEsa0JBQWtCOzRCQUM3Qjs0QkFDQTs0QkFDQTt5QkFDRDtvQkFDSDs0REFBRztZQUVMLE9BQU8sSUFBSSxDQUFDUixZQUFZTSxnQkFBZ0I7Z0JBQ3RDLG1EQUFtRDtnQkFDbkRvRDs2REFBVzt3QkFDVC9DO29CQUNGOzREQUFHO1lBQ0w7UUFDRjtnREFBRztRQUFDWDtRQUFVTTtRQUFnQkc7UUFBZUU7UUFBZUo7UUFBWUM7S0FBYTtJQUVyRkwsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QjtRQUFFRTtRQUFnQnFELGFBQWF0RCxNQUFNTyxNQUFNO1FBQUVnRCxjQUFjdEQsa0JBQWtCRCxNQUFNTyxNQUFNLEdBQUc7SUFBRTtJQUVqSSxJQUFJLENBQUNOLGtCQUFrQkQsTUFBTU8sTUFBTSxLQUFLLEdBQUc7UUFDekNULFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NFLGdCQUFnQixVQUFVRCxNQUFNTyxNQUFNO1FBQ3hGLE9BQU87SUFDVDtJQUVBVCxRQUFRQyxHQUFHLENBQUMsNkNBQTZDQztJQUV6RCxxQkFDRSw4REFBQ3dEO1FBQUkzRCxXQUFXQTtrQkFDZCw0RUFBQ04scUVBQTRCQTtZQUMzQlMsT0FBT0E7WUFDUEwsVUFBVU07WUFDVndELFlBQVk7Ozs7Ozs7Ozs7O0FBSXBCO0dBalN3Qi9EOztRQWVsQkYscUZBQXdCQTs7O0tBZk5FIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcT3JjaGVzdHJhdGlvblByb2dyZXNzQnJpZGdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IE9yY2hlc3RyYXRpb25Qcm9ncmVzc1RyYWNrZXIgZnJvbSAnLi9PcmNoZXN0cmF0aW9uUHJvZ3Jlc3NUcmFja2VyJztcbmltcG9ydCB7IHVzZU9yY2hlc3RyYXRpb25UcmFja2luZywgT1JDSEVTVFJBVElPTl9TVEVQUyB9IGZyb20gJ0AvaG9va3MvdXNlT3JjaGVzdHJhdGlvblRyYWNraW5nJztcbmltcG9ydCB7IHR5cGUgUHJvZ3Jlc3NDYWxsYmFjayB9IGZyb20gJ0AvbGliL2xhbmdncmFwaC1vcmNoZXN0cmF0aW9uL1JvdUtleUxhbmdHcmFwaEludGVncmF0aW9uJztcblxuaW50ZXJmYWNlIE9yY2hlc3RyYXRpb25Qcm9ncmVzc0JyaWRnZVByb3BzIHtcbiAgaXNBY3RpdmU6IGJvb2xlYW47XG4gIG9uUHJvZ3Jlc3NDYWxsYmFjaz86IChjYWxsYmFjazogUHJvZ3Jlc3NDYWxsYmFjaykgPT4gdm9pZDtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBPcmNoZXN0cmF0aW9uUHJvZ3Jlc3NCcmlkZ2Uoe1xuICBpc0FjdGl2ZSxcbiAgb25Qcm9ncmVzc0NhbGxiYWNrLFxuICBjbGFzc05hbWUgPSAnJ1xufTogT3JjaGVzdHJhdGlvblByb2dyZXNzQnJpZGdlUHJvcHMpIHtcbiAgY29uc29sZS5sb2coJ/Cfjq8gT3JjaGVzdHJhdGlvblByb2dyZXNzQnJpZGdlIHJlbmRlcjonLCB7IGlzQWN0aXZlLCBjbGFzc05hbWUgfSk7XG5cbiAgY29uc3Qge1xuICAgIHN0ZXBzLFxuICAgIGlzQWN0aXZlOiB0cmFja2luZ0FjdGl2ZSxcbiAgICB0cmFja0V2ZW50LFxuICAgIGNvbXBsZXRlU3RlcCxcbiAgICBzdGFydFRyYWNraW5nLFxuICAgIGNvbXBsZXRlVHJhY2tpbmcsXG4gICAgcmVzZXRUcmFja2luZ1xuICB9ID0gdXNlT3JjaGVzdHJhdGlvblRyYWNraW5nKCk7XG5cbiAgY29uc29sZS5sb2coJ/Cfjq8gUHJvZ3Jlc3Mgc3RhdGU6JywgeyBzdGVwczogc3RlcHMubGVuZ3RoLCB0cmFja2luZ0FjdGl2ZSwgaXNBY3RpdmUgfSk7XG5cbiAgLy8gQ3JlYXRlIHByb2dyZXNzIGNhbGxiYWNrIGZvciBvcmNoZXN0cmF0aW9uIHN5c3RlbVxuICBjb25zdCBwcm9ncmVzc0NhbGxiYWNrOiBQcm9ncmVzc0NhbGxiYWNrID0gdXNlQ2FsbGJhY2soe1xuICAgIG9uQ2xhc3NpZmljYXRpb25TdGFydDogKCkgPT4ge1xuICAgICAgdHJhY2tFdmVudCh7XG4gICAgICAgIHR5cGU6ICdjbGFzc2lmaWNhdGlvbicsXG4gICAgICAgIHN0ZXBJZDogJ2FuYWx5emluZ19yZXF1ZXN0JyxcbiAgICAgICAgdGl0bGU6ICdBbmFseXppbmcgcmVxdWVzdCB3aXRoIFJvdUtleSBBSScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnVW5kZXJzdGFuZGluZyB5b3VyIHJlcXVlc3QgYW5kIHJlcXVpcmVtZW50cy4uLicsXG4gICAgICAgIGRldGFpbHM6IFtcbiAgICAgICAgICAnUHJvY2Vzc2luZyBuYXR1cmFsIGxhbmd1YWdlIGlucHV0JyxcbiAgICAgICAgICAnSWRlbnRpZnlpbmcgdGFzayBjb21wbGV4aXR5IGFuZCBzY29wZScsXG4gICAgICAgICAgJ0RldGVybWluaW5nIHNwZWNpYWxpc3QgZXhwZXJ0aXNlIG5lZWRlZCcsXG4gICAgICAgICAgJ1ByZXBhcmluZyBmb3Igcm9sZSBjbGFzc2lmaWNhdGlvbidcbiAgICAgICAgXVxuICAgICAgfSk7XG4gICAgfSxcblxuICAgIG9uQ2xhc3NpZmljYXRpb25Db21wbGV0ZTogKHJvbGVzOiBzdHJpbmdbXSwgdGhyZXNob2xkOiBudW1iZXIpID0+IHtcbiAgICAgIGNvbXBsZXRlU3RlcCgnYW5hbHl6aW5nX3JlcXVlc3QnLCBbXG4gICAgICAgIGDinIUgUmVxdWVzdCBhbmFseXNpcyBjb21wbGV0ZWAsXG4gICAgICAgIGDwn46vIElkZW50aWZpZWQgJHtyb2xlcy5sZW5ndGh9IHNwZWNpYWxpc3QgYXJlYXMgbmVlZGVkYCxcbiAgICAgICAgYPCfk4ogQ2xhc3NpZmljYXRpb24gdGhyZXNob2xkOiAke3RocmVzaG9sZH1gLFxuICAgICAgICBg8J+agCBSZWFkeSB0byBwcmVwYXJlIEFJIHNwZWNpYWxpc3RzYFxuICAgICAgXSk7XG5cbiAgICAgIC8vIEFkZCBkZXRhaWxlZCByb2xlIGJyZWFrZG93blxuICAgICAgdHJhY2tFdmVudCh7XG4gICAgICAgIHR5cGU6ICdyb2xlX3NlbGVjdGlvbicsXG4gICAgICAgIHN0ZXBJZDogJ3ByZXBhcmluZ19zcGVjaWFsaXN0cycsXG4gICAgICAgIHRpdGxlOiAnUHJlcGFyaW5nIEFJIFNwZWNpYWxpc3RzJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdTZXR0aW5nIHVwIHRoZSBwZXJmZWN0IGV4cGVydCB0ZWFtIGZvciB5b3VyIHRhc2suLi4nLFxuICAgICAgICBkZXRhaWxzOiBbXG4gICAgICAgICAgYFNlbGVjdGVkIHJvbGVzOiAke3JvbGVzLmpvaW4oJywgJyl9YCxcbiAgICAgICAgICAnQ29uZmlndXJpbmcgc3BlY2lhbGlzdCBjYXBhYmlsaXRpZXMnLFxuICAgICAgICAgICdBc3NpZ25pbmcgQVBJIGtleXMgdG8gZWFjaCByb2xlJyxcbiAgICAgICAgICAnUHJlcGFyaW5nIGNvbGxhYm9yYXRpdmUgd29ya2Zsb3cnXG4gICAgICAgIF1cbiAgICAgIH0pO1xuICAgIH0sXG4gICAgXG4gICAgb25Sb2xlU2VsZWN0aW9uQ29tcGxldGU6IChzZWxlY3RlZFJvbGVzOiBzdHJpbmdbXSwgZmlsdGVyZWRSb2xlczogc3RyaW5nW10pID0+IHtcbiAgICAgIGNvbXBsZXRlU3RlcCgncHJlcGFyaW5nX3NwZWNpYWxpc3RzJywgW1xuICAgICAgICBg4pyFIFNwZWNpYWxpc3QgdGVhbSBjb25maWd1cmVkYCxcbiAgICAgICAgYPCfkaUgJHtzZWxlY3RlZFJvbGVzLmxlbmd0aH0gZXhwZXJ0cyBzZWxlY3RlZDogJHtzZWxlY3RlZFJvbGVzLmpvaW4oJywgJyl9YCxcbiAgICAgICAgYPCflKcgQVBJIGtleXMgYXNzaWduZWQgYW5kIHZhbGlkYXRlZGAsXG4gICAgICAgIGDimqEgUmVhZHkgZm9yIHdvcmtmbG93IG9yY2hlc3RyYXRpb25gXG4gICAgICBdKTtcbiAgICB9LFxuICAgIFxuICAgIG9uV29ya2Zsb3dTZWxlY3Rpb25Db21wbGV0ZTogKHdvcmtmbG93VHlwZTogc3RyaW5nLCByZWFzb25pbmc6IHN0cmluZykgPT4ge1xuICAgICAgY29tcGxldGVTdGVwKCdvcmNoZXN0cmF0aW9uX3BsYW5uaW5nJywgW1xuICAgICAgICBg4pyFIE9yY2hlc3RyYXRpb24gc3RyYXRlZ3kgZmluYWxpemVkYCxcbiAgICAgICAgYPCfjq8gU2VsZWN0ZWQgYXBwcm9hY2g6ICR7d29ya2Zsb3dUeXBlLnRvVXBwZXJDYXNlKCl9YCxcbiAgICAgICAgYPCfkqEgU3RyYXRlZ3k6ICR7cmVhc29uaW5nfWAsXG4gICAgICAgIGDwn5qAIFJlYWR5IHRvIGNyZWF0ZSBBSSBhZ2VudHNgXG4gICAgICBdKTtcbiAgICB9LFxuICAgIFxuICAgIG9uQWdlbnRDcmVhdGlvblN0YXJ0OiAoKSA9PiB7XG4gICAgICB0cmFja0V2ZW50KHtcbiAgICAgICAgdHlwZTogJ2FnZW50X2NyZWF0aW9uJyxcbiAgICAgICAgc3RlcElkOiAnY3JlYXRpbmdfYWdlbnRzJyxcbiAgICAgICAgdGl0bGU6ICdDcmVhdGluZyBBSSBBZ2VudHMnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0luaXRpYWxpemluZyBzcGVjaWFsaXplZCBBSSBhZ2VudHMgd2l0aCB5b3VyIEFQSSBrZXlzLi4uJyxcbiAgICAgICAgZGV0YWlsczogW1xuICAgICAgICAgICdDb25maWd1cmluZyBhZ2VudCBwZXJzb25hbGl0aWVzIGFuZCBjYXBhYmlsaXRpZXMnLFxuICAgICAgICAgICdBc3NpZ25pbmcgQVBJIGtleXMgdG8gZWFjaCBzcGVjaWFsaXN0JyxcbiAgICAgICAgICAnU2V0dGluZyB1cCBjb21tdW5pY2F0aW9uIHByb3RvY29scycsXG4gICAgICAgICAgJ1ByZXBhcmluZyBmb3IgY29sbGFib3JhdGl2ZSB3b3JrJ1xuICAgICAgICBdXG4gICAgICB9KTtcbiAgICB9LFxuXG4gICAgb25BZ2VudENyZWF0aW9uQ29tcGxldGU6IChhZ2VudHM6IEFycmF5PHsgcm9sZTogc3RyaW5nLCBhcGlLZXk6IHN0cmluZyB9PikgPT4ge1xuICAgICAgY29tcGxldGVTdGVwKCdjcmVhdGluZ19hZ2VudHMnLCBbXG4gICAgICAgIGDinIUgJHthZ2VudHMubGVuZ3RofSBBSSBhZ2VudHMgY3JlYXRlZCBzdWNjZXNzZnVsbHlgLFxuICAgICAgICBg8J+UkSBBUEkga2V5czogJHthZ2VudHMubWFwKGEgPT4gYCR7YS5yb2xlfSAoJHthLmFwaUtleX0pYCkuam9pbignLCAnKX1gLFxuICAgICAgICBg8J+kliBBbGwgYWdlbnRzIHJlYWR5IGZvciBjb2xsYWJvcmF0aW9uYCxcbiAgICAgICAgYOKaoSBUZWFtIGFzc2VtYmx5IGNvbXBsZXRlYFxuICAgICAgXSk7XG4gICAgfSxcbiAgICBcbiAgICBvblN1cGVydmlzb3JJbml0U3RhcnQ6ICgpID0+IHtcbiAgICAgIHRyYWNrRXZlbnQoe1xuICAgICAgICB0eXBlOiAnc3VwZXJ2aXNvcl9pbml0JyxcbiAgICAgICAgc3RlcElkOiAnc3VwZXJ2aXNvcl9zZXR1cCcsXG4gICAgICAgIHRpdGxlOiAnU2V0dGluZyBVcCBUZWFtIENvb3JkaW5hdG9yJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdJbml0aWFsaXppbmcgc3VwZXJ2aXNvciBhZ2VudCB0byBtYW5hZ2UgY29sbGFib3JhdGlvbi4uLicsXG4gICAgICAgIGRldGFpbHM6IFtcbiAgICAgICAgICAnU2VsZWN0aW5nIG1vc3QgY2FwYWJsZSBhZ2VudCBhcyBzdXBlcnZpc29yJyxcbiAgICAgICAgICAnRXN0YWJsaXNoaW5nIGNvbW11bmljYXRpb24gcHJvdG9jb2xzJyxcbiAgICAgICAgICAnU2V0dGluZyB1cCB0YXNrIGRlbGVnYXRpb24gc3lzdGVtJyxcbiAgICAgICAgICAnUHJlcGFyaW5nIGNvb3JkaW5hdGlvbiBzdHJhdGVnaWVzJ1xuICAgICAgICBdXG4gICAgICB9KTtcbiAgICB9LFxuXG4gICAgb25TdXBlcnZpc29ySW5pdENvbXBsZXRlOiAoc3VwZXJ2aXNvclJvbGU6IHN0cmluZykgPT4ge1xuICAgICAgY29tcGxldGVTdGVwKCdzdXBlcnZpc29yX3NldHVwJywgW1xuICAgICAgICBg4pyFIFRlYW0gY29vcmRpbmF0b3IgcmVhZHk6ICR7c3VwZXJ2aXNvclJvbGV9YCxcbiAgICAgICAgYPCfk4sgQ29tbXVuaWNhdGlvbiBjaGFubmVscyBlc3RhYmxpc2hlZGAsXG4gICAgICAgIGDwn46vIFRhc2sgZGVsZWdhdGlvbiBzeXN0ZW0gYWN0aXZlYCxcbiAgICAgICAgYOKaoSBSZWFkeSB0byBtYW5hZ2UgY29sbGFib3JhdGl2ZSB3b3JrZmxvd2BcbiAgICAgIF0pO1xuICAgIH0sXG4gICAgXG4gICAgb25UYXNrUGxhbm5pbmdTdGFydDogKCkgPT4ge1xuICAgICAgdHJhY2tFdmVudChPUkNIRVNUUkFUSU9OX1NURVBTLk1VTFRJX1JPTEUuVEFTS19QTEFOTklORyk7XG4gICAgfSxcbiAgICBcbiAgICBvblRhc2tQbGFubmluZ0NvbXBsZXRlOiAocGxhbjogc3RyaW5nKSA9PiB7XG4gICAgICBjb21wbGV0ZVN0ZXAoJ3Rhc2tfcGxhbm5pbmcnLCBbXG4gICAgICAgICdXb3JrIGRpc3RyaWJ1dGlvbiBzdHJhdGVneSBmaW5hbGl6ZWQnLFxuICAgICAgICAnRWFjaCBzcGVjaWFsaXN0IGtub3dzIHRoZWlyIHJvbGUgYW5kIHJlc3BvbnNpYmlsaXRpZXMnLFxuICAgICAgICAnVGVhbSByZWFkeSB0byBiZWdpbiBjb2xsYWJvcmF0aXZlIHdvcmsnXG4gICAgICBdKTtcbiAgICB9LFxuICAgIFxuICAgIG9uQWdlbnRXb3JrU3RhcnQ6IChyb2xlOiBzdHJpbmcsIHRhc2s6IHN0cmluZykgPT4ge1xuICAgICAgY29uc3Qgcm9sZU5hbWVzID0ge1xuICAgICAgICAnYnJhaW5zdG9ybWluZ19pZGVhdGlvbic6ICdDcmVhdGl2ZSBJZGVhdGlvbiBTcGVjaWFsaXN0JyxcbiAgICAgICAgJ3dyaXRpbmcnOiAnQ29udGVudCBXcml0aW5nIEV4cGVydCcsXG4gICAgICAgICdjb2RpbmdfYmFja2VuZCc6ICdCYWNrZW5kIERldmVsb3BtZW50IEV4cGVydCcsXG4gICAgICAgICdjb2RpbmdfZnJvbnRlbmQnOiAnRnJvbnRlbmQgRGV2ZWxvcG1lbnQgRXhwZXJ0JyxcbiAgICAgICAgJ2dlbmVyYWxfY2hhdCc6ICdHZW5lcmFsIEFJIEFzc2lzdGFudCdcbiAgICAgIH07XG4gICAgICBcbiAgICAgIHRyYWNrRXZlbnQoe1xuICAgICAgICB0eXBlOiAnYWdlbnRfd29ya2luZycsXG4gICAgICAgIHN0ZXBJZDogYGFnZW50XyR7cm9sZX1gLFxuICAgICAgICB0aXRsZTogYCR7cm9sZU5hbWVzW3JvbGUgYXMga2V5b2YgdHlwZW9mIHJvbGVOYW1lc10gfHwgJ0FJIFNwZWNpYWxpc3QnfSBXb3JraW5nYCxcbiAgICAgICAgZGVzY3JpcHRpb246IHRhc2ssXG4gICAgICAgIGRldGFpbHM6IFtcbiAgICAgICAgICBgU3BlY2lhbGlzdDogJHtyb2xlTmFtZXNbcm9sZSBhcyBrZXlvZiB0eXBlb2Ygcm9sZU5hbWVzXSB8fCByb2xlfWAsXG4gICAgICAgICAgJ0FwcGx5aW5nIGV4cGVydGlzZSB0byB5b3VyIHJlcXVlc3QnLFxuICAgICAgICAgICdQcm9jZXNzaW5nIGFuZCBnZW5lcmF0aW5nIHJlc3BvbnNlLi4uJ1xuICAgICAgICBdXG4gICAgICB9KTtcbiAgICB9LFxuICAgIFxuICAgIG9uQWdlbnRXb3JrQ29tcGxldGU6IChyb2xlOiBzdHJpbmcsIHJlc3VsdDogc3RyaW5nKSA9PiB7XG4gICAgICBjb25zdCByb2xlTmFtZXMgPSB7XG4gICAgICAgICdicmFpbnN0b3JtaW5nX2lkZWF0aW9uJzogJ0NyZWF0aXZlIElkZWF0aW9uIFNwZWNpYWxpc3QnLFxuICAgICAgICAnd3JpdGluZyc6ICdDb250ZW50IFdyaXRpbmcgRXhwZXJ0JyxcbiAgICAgICAgJ2NvZGluZ19iYWNrZW5kJzogJ0JhY2tlbmQgRGV2ZWxvcG1lbnQgRXhwZXJ0JyxcbiAgICAgICAgJ2NvZGluZ19mcm9udGVuZCc6ICdGcm9udGVuZCBEZXZlbG9wbWVudCBFeHBlcnQnLFxuICAgICAgICAnZ2VuZXJhbF9jaGF0JzogJ0dlbmVyYWwgQUkgQXNzaXN0YW50J1xuICAgICAgfTtcbiAgICAgIFxuICAgICAgY29tcGxldGVTdGVwKGBhZ2VudF8ke3JvbGV9YCwgW1xuICAgICAgICBgJHtyb2xlTmFtZXNbcm9sZSBhcyBrZXlvZiB0eXBlb2Ygcm9sZU5hbWVzXSB8fCAnU3BlY2lhbGlzdCd9IHdvcmsgY29tcGxldGVkYCxcbiAgICAgICAgYEhpZ2gtcXVhbGl0eSByZXNwb25zZSBnZW5lcmF0ZWRgLFxuICAgICAgICAnQ29udHJpYnV0aW9uIHJlYWR5IGZvciB0ZWFtIGludGVncmF0aW9uJ1xuICAgICAgXSk7XG4gICAgfSxcbiAgICBcbiAgICBvblN1cGVydmlzb3JTeW50aGVzaXNTdGFydDogKCkgPT4ge1xuICAgICAgdHJhY2tFdmVudCh7XG4gICAgICAgIHR5cGU6ICdzdXBlcnZpc29yX3N5bnRoZXNpcycsXG4gICAgICAgIHN0ZXBJZDogJ3N1cGVydmlzb3Jfc3ludGhlc2lzJyxcbiAgICAgICAgdGl0bGU6ICdUZWFtIENvb3JkaW5hdG9yOiBDb21iaW5pbmcgUmVzdWx0cycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQ3JlYXRpbmcgY29tcHJlaGVuc2l2ZSBmaW5hbCByZXNwb25zZS4uLicsXG4gICAgICAgIGRldGFpbHM6IFtcbiAgICAgICAgICAnUmV2aWV3aW5nIGFsbCBzcGVjaWFsaXN0IGNvbnRyaWJ1dGlvbnMnLFxuICAgICAgICAgICdDb21iaW5pbmcgaW5zaWdodHMgaW50byB1bmlmaWVkIHJlc3BvbnNlJyxcbiAgICAgICAgICAnRW5zdXJpbmcgcXVhbGl0eSBhbmQgY29oZXJlbmNlJ1xuICAgICAgICBdXG4gICAgICB9KTtcbiAgICB9LFxuICAgIFxuICAgIG9uU3VwZXJ2aXNvclN5bnRoZXNpc0NvbXBsZXRlOiAoc3ludGhlc2lzOiBzdHJpbmcpID0+IHtcbiAgICAgIGNvbXBsZXRlU3RlcCgnc3VwZXJ2aXNvcl9zeW50aGVzaXMnLCBbXG4gICAgICAgICdGaW5hbCByZXNwb25zZSBzeW50aGVzaXMgY29tcGxldGVkJyxcbiAgICAgICAgJ0FsbCBzcGVjaWFsaXN0IGluc2lnaHRzIHN1Y2Nlc3NmdWxseSBjb21iaW5lZCcsXG4gICAgICAgICdDb2xsYWJvcmF0aXZlIHdvcmsgZmluaXNoZWQgc3VjY2Vzc2Z1bGx5J1xuICAgICAgXSk7XG4gICAgfSxcbiAgICBcbiAgICBvbk9yY2hlc3RyYXRpb25Db21wbGV0ZTogKHJlc3VsdDogYW55KSA9PiB7XG4gICAgICBjb21wbGV0ZVRyYWNraW5nKCk7XG4gICAgfSxcbiAgICBcbiAgICBvbkVycm9yOiAoc3RlcDogc3RyaW5nLCBlcnJvcjogc3RyaW5nKSA9PiB7XG4gICAgICB0cmFja0V2ZW50KHtcbiAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgc3RlcElkOiBgZXJyb3JfJHtzdGVwfWAsXG4gICAgICAgIHRpdGxlOiBgRXJyb3IgaW4gJHtzdGVwfWAsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvcixcbiAgICAgICAgZGV0YWlsczogW1xuICAgICAgICAgIGBTdGVwOiAke3N0ZXB9YCxcbiAgICAgICAgICBgRXJyb3I6ICR7ZXJyb3J9YCxcbiAgICAgICAgICAnQXR0ZW1wdGluZyByZWNvdmVyeS4uLidcbiAgICAgICAgXVxuICAgICAgfSk7XG4gICAgfVxuICB9LCBbdHJhY2tFdmVudCwgY29tcGxldGVTdGVwLCBjb21wbGV0ZVRyYWNraW5nXSk7XG5cbiAgLy8gUHJvdmlkZSBwcm9ncmVzcyBjYWxsYmFjayB0byBwYXJlbnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAob25Qcm9ncmVzc0NhbGxiYWNrKSB7XG4gICAgICBvblByb2dyZXNzQ2FsbGJhY2socHJvZ3Jlc3NDYWxsYmFjayk7XG4gICAgfVxuICB9LCBbb25Qcm9ncmVzc0NhbGxiYWNrLCBwcm9ncmVzc0NhbGxiYWNrXSk7XG5cbiAgLy8gU3RhcnQvc3RvcCB0cmFja2luZyBiYXNlZCBvbiBpc0FjdGl2ZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc0FjdGl2ZSAmJiAhdHJhY2tpbmdBY3RpdmUpIHtcbiAgICAgIHN0YXJ0VHJhY2tpbmcoKTtcblxuICAgICAgLy8gRm9yIHRlc3Rpbmc6IHNpbXVsYXRlIGEgc2ltcGxlIHByb2dyZXNzIGZsb3dcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICB0cmFja0V2ZW50KHtcbiAgICAgICAgICB0eXBlOiAnY2xhc3NpZmljYXRpb24nLFxuICAgICAgICAgIHN0ZXBJZDogJ2NsYXNzaWZpY2F0aW9uJyxcbiAgICAgICAgICB0aXRsZTogJ0FuYWx5emluZyByZXF1ZXN0IHdpdGggUm91S2V5IEFJJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ1VuZGVyc3RhbmRpbmcgeW91ciByZXF1ZXN0IGFuZCByZXF1aXJlbWVudHMuLi4nXG4gICAgICAgIH0pO1xuICAgICAgfSwgNTAwKTtcblxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGNvbXBsZXRlU3RlcCgnY2xhc3NpZmljYXRpb24nLCBbXG4gICAgICAgICAgJ1JlcXVlc3QgYW5hbHlzaXMgY29tcGxldGVkJyxcbiAgICAgICAgICAnT3B0aW1hbCBhcHByb2FjaCBpZGVudGlmaWVkJyxcbiAgICAgICAgICAnUmVhZHkgdG8gcHJvY2VzcyB5b3VyIHJlcXVlc3QnXG4gICAgICAgIF0pO1xuICAgICAgfSwgMTUwMCk7XG5cbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICB0cmFja0V2ZW50KHtcbiAgICAgICAgICB0eXBlOiAnYWdlbnRfY3JlYXRpb24nLFxuICAgICAgICAgIHN0ZXBJZDogJ2FnZW50X2NyZWF0aW9uJyxcbiAgICAgICAgICB0aXRsZTogJ1ByZXBhcmluZyBBSSBTcGVjaWFsaXN0JyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ1NldHRpbmcgdXAgdGhlIHBlcmZlY3QgZXhwZXJ0IGZvciB5b3VyIHRhc2suLi4nXG4gICAgICAgIH0pO1xuICAgICAgfSwgMjAwMCk7XG5cbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBjb21wbGV0ZVN0ZXAoJ2FnZW50X2NyZWF0aW9uJywgW1xuICAgICAgICAgICdBSSBzcGVjaWFsaXN0IHJlYWR5IHRvIHdvcmsnLFxuICAgICAgICAgICdFeHBlcnQgY29uZmlndXJlZCB3aXRoIG9wdGltYWwgc2V0dGluZ3MnLFxuICAgICAgICAgICdQcm9jZXNzaW5nIHlvdXIgcmVxdWVzdC4uLidcbiAgICAgICAgXSk7XG4gICAgICB9LCAzMDAwKTtcblxuICAgIH0gZWxzZSBpZiAoIWlzQWN0aXZlICYmIHRyYWNraW5nQWN0aXZlKSB7XG4gICAgICAvLyBTbWFsbCBkZWxheSBiZWZvcmUgcmVzZXR0aW5nIHRvIHNob3cgZmluYWwgc3RhdGVcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICByZXNldFRyYWNraW5nKCk7XG4gICAgICB9LCAzMDAwKTtcbiAgICB9XG4gIH0sIFtpc0FjdGl2ZSwgdHJhY2tpbmdBY3RpdmUsIHN0YXJ0VHJhY2tpbmcsIHJlc2V0VHJhY2tpbmcsIHRyYWNrRXZlbnQsIGNvbXBsZXRlU3RlcF0pO1xuXG4gIGNvbnNvbGUubG9nKCfwn46vIFJlbmRlciBkZWNpc2lvbjonLCB7IHRyYWNraW5nQWN0aXZlLCBzdGVwc0xlbmd0aDogc3RlcHMubGVuZ3RoLCBzaG91bGRSZW5kZXI6IHRyYWNraW5nQWN0aXZlICYmIHN0ZXBzLmxlbmd0aCA+IDAgfSk7XG5cbiAgaWYgKCF0cmFja2luZ0FjdGl2ZSB8fCBzdGVwcy5sZW5ndGggPT09IDApIHtcbiAgICBjb25zb2xlLmxvZygn8J+OryBOb3QgcmVuZGVyaW5nIC0gdHJhY2tpbmdBY3RpdmU6JywgdHJhY2tpbmdBY3RpdmUsICdzdGVwczonLCBzdGVwcy5sZW5ndGgpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgY29uc29sZS5sb2coJ/Cfjq8gUmVuZGVyaW5nIHByb2dyZXNzIHRyYWNrZXIgd2l0aCBzdGVwczonLCBzdGVwcyk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgIDxPcmNoZXN0cmF0aW9uUHJvZ3Jlc3NUcmFja2VyXG4gICAgICAgIHN0ZXBzPXtzdGVwc31cbiAgICAgICAgaXNBY3RpdmU9e3RyYWNraW5nQWN0aXZlfVxuICAgICAgICBhdXRvU2Nyb2xsPXt0cnVlfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwiT3JjaGVzdHJhdGlvblByb2dyZXNzVHJhY2tlciIsInVzZU9yY2hlc3RyYXRpb25UcmFja2luZyIsIk9SQ0hFU1RSQVRJT05fU1RFUFMiLCJPcmNoZXN0cmF0aW9uUHJvZ3Jlc3NCcmlkZ2UiLCJpc0FjdGl2ZSIsIm9uUHJvZ3Jlc3NDYWxsYmFjayIsImNsYXNzTmFtZSIsImNvbnNvbGUiLCJsb2ciLCJzdGVwcyIsInRyYWNraW5nQWN0aXZlIiwidHJhY2tFdmVudCIsImNvbXBsZXRlU3RlcCIsInN0YXJ0VHJhY2tpbmciLCJjb21wbGV0ZVRyYWNraW5nIiwicmVzZXRUcmFja2luZyIsImxlbmd0aCIsInByb2dyZXNzQ2FsbGJhY2siLCJvbkNsYXNzaWZpY2F0aW9uU3RhcnQiLCJ0eXBlIiwic3RlcElkIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImRldGFpbHMiLCJvbkNsYXNzaWZpY2F0aW9uQ29tcGxldGUiLCJyb2xlcyIsInRocmVzaG9sZCIsImpvaW4iLCJvblJvbGVTZWxlY3Rpb25Db21wbGV0ZSIsInNlbGVjdGVkUm9sZXMiLCJmaWx0ZXJlZFJvbGVzIiwib25Xb3JrZmxvd1NlbGVjdGlvbkNvbXBsZXRlIiwid29ya2Zsb3dUeXBlIiwicmVhc29uaW5nIiwidG9VcHBlckNhc2UiLCJvbkFnZW50Q3JlYXRpb25TdGFydCIsIm9uQWdlbnRDcmVhdGlvbkNvbXBsZXRlIiwiYWdlbnRzIiwibWFwIiwiYSIsInJvbGUiLCJhcGlLZXkiLCJvblN1cGVydmlzb3JJbml0U3RhcnQiLCJvblN1cGVydmlzb3JJbml0Q29tcGxldGUiLCJzdXBlcnZpc29yUm9sZSIsIm9uVGFza1BsYW5uaW5nU3RhcnQiLCJNVUxUSV9ST0xFIiwiVEFTS19QTEFOTklORyIsIm9uVGFza1BsYW5uaW5nQ29tcGxldGUiLCJwbGFuIiwib25BZ2VudFdvcmtTdGFydCIsInRhc2siLCJyb2xlTmFtZXMiLCJvbkFnZW50V29ya0NvbXBsZXRlIiwicmVzdWx0Iiwib25TdXBlcnZpc29yU3ludGhlc2lzU3RhcnQiLCJvblN1cGVydmlzb3JTeW50aGVzaXNDb21wbGV0ZSIsInN5bnRoZXNpcyIsIm9uT3JjaGVzdHJhdGlvbkNvbXBsZXRlIiwib25FcnJvciIsInN0ZXAiLCJlcnJvciIsInNldFRpbWVvdXQiLCJzdGVwc0xlbmd0aCIsInNob3VsZFJlbmRlciIsImRpdiIsImF1dG9TY3JvbGwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});