"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationSSE */ \"(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, sessionId, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        sessionId,\n        className\n    });\n    // Use SSE-based progress tracking\n    const { steps, isActive: sseActive, isComplete, error, connect, disconnect, reset } = (0,_hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE)(sessionId);\n    console.log('🎯 SSE Progress state:', {\n        steps: steps.length,\n        sseActive,\n        isActive,\n        sessionId,\n        isComplete,\n        error\n    });\n    // Create a dummy progress callback for backward compatibility\n    // The real progress tracking now happens via SSE\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Classification started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                console.log('[Progress Bridge] Classification complete (SSE mode):', roles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                console.log('[Progress Bridge] Role selection complete (SSE mode):', selectedRoles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                console.log('[Progress Bridge] Workflow selection complete (SSE mode):', workflowType);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Agent creation started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                console.log('[Progress Bridge] Agent creation complete (SSE mode):', agents.length);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Supervisor init started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                console.log('[Progress Bridge] Supervisor init complete (SSE mode):', supervisorRole);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Task planning started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                console.log('[Progress Bridge] Task planning complete (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    \"✅ Final response synthesis completed\",\n                    \"\\uD83C\\uDFAF All specialist insights successfully combined\",\n                    \"\\uD83D\\uDCDD Comprehensive response ready\",\n                    \"⚡ Collaborative work finished successfully\"\n                ]);\n                // Add final \"generating response\" step\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        trackEvent({\n                            type: 'orchestration_complete',\n                            stepId: 'generating_response',\n                            title: 'Generating Response',\n                            description: 'Finalizing and streaming your comprehensive response...',\n                            details: [\n                                'Formatting final response',\n                                'Preparing for streaming delivery',\n                                'Quality assurance complete',\n                                'Ready to deliver results'\n                            ]\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 200);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                var _result_metadata, _result_metadata1;\n                completeStep('generating_response', [\n                    \"✅ Response generation complete\",\n                    \"\\uD83D\\uDCCA Total tokens: \".concat(((_result_metadata = result.metadata) === null || _result_metadata === void 0 ? void 0 : _result_metadata.totalTokens) || 'N/A'),\n                    \"⏱️ Execution time: \".concat(((_result_metadata1 = result.metadata) === null || _result_metadata1 === void 0 ? void 0 : _result_metadata1.executionTime) || 'N/A', \"ms\"),\n                    \"\\uD83C\\uDF89 Multi-role orchestration successful!\"\n                ]);\n                // Complete tracking after a short delay to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        completeTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 2000);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"MV7b4B3Euz5CPsltLA582nV5DWw=\", false, function() {\n    return [\n        _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});