"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationSSE */ \"(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, sessionId, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        sessionId,\n        className\n    });\n    // Use SSE-based progress tracking\n    const { steps, isActive: sseActive, isComplete, error, connect, disconnect, reset } = (0,_hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE)(sessionId);\n    console.log('🎯 SSE Progress state:', {\n        steps: steps.length,\n        sseActive,\n        isActive,\n        sessionId,\n        isComplete,\n        error\n    });\n    // Create a dummy progress callback for backward compatibility\n    // The real progress tracking now happens via SSE\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Classification started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                console.log('[Progress Bridge] Classification complete (SSE mode):', roles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                console.log('[Progress Bridge] Role selection complete (SSE mode):', selectedRoles);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                console.log('[Progress Bridge] Workflow selection complete (SSE mode):', workflowType);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Agent creation started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                console.log('[Progress Bridge] Agent creation complete (SSE mode):', agents.length);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Supervisor init started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                console.log('[Progress Bridge] Supervisor init complete (SSE mode):', supervisorRole);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                console.log('[Progress Bridge] Task planning started (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                console.log('[Progress Bridge] Task planning complete (SSE mode)');\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                console.log('[Progress Bridge] Agent work started (SSE mode):', role);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                console.log('[Progress Bridge] Agent work complete (SSE mode):', role);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    \"✅ Final response synthesis completed\",\n                    \"\\uD83C\\uDFAF All specialist insights successfully combined\",\n                    \"\\uD83D\\uDCDD Comprehensive response ready\",\n                    \"⚡ Collaborative work finished successfully\"\n                ]);\n                // Add final \"generating response\" step\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        trackEvent({\n                            type: 'orchestration_complete',\n                            stepId: 'generating_response',\n                            title: 'Generating Response',\n                            description: 'Finalizing and streaming your comprehensive response...',\n                            details: [\n                                'Formatting final response',\n                                'Preparing for streaming delivery',\n                                'Quality assurance complete',\n                                'Ready to deliver results'\n                            ]\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 200);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                var _result_metadata, _result_metadata1;\n                completeStep('generating_response', [\n                    \"✅ Response generation complete\",\n                    \"\\uD83D\\uDCCA Total tokens: \".concat(((_result_metadata = result.metadata) === null || _result_metadata === void 0 ? void 0 : _result_metadata.totalTokens) || 'N/A'),\n                    \"⏱️ Execution time: \".concat(((_result_metadata1 = result.metadata) === null || _result_metadata1 === void 0 ? void 0 : _result_metadata1.executionTime) || 'N/A', \"ms\"),\n                    \"\\uD83C\\uDF89 Multi-role orchestration successful!\"\n                ]);\n                // Complete tracking after a short delay to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        completeTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 2000);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"MV7b4B3Euz5CPsltLA582nV5DWw=\", false, function() {\n    return [\n        _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});