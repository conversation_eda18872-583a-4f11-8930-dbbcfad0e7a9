"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressCard.tsx":
/*!******************************************************!*\
  !*** ./src/components/OrchestrationProgressCard.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BeakerIcon,BoltIcon,CheckCircleIcon,ChevronDownIcon,ChevronRightIcon,ClockIcon,CogIcon,CpuChipIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,SparklesIcon,UserGroupIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst STEP_ICONS = {\n    analysis: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    roles: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    workflow: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    agents: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    supervisor: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    planning: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    working: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    synthesis: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    connecting: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    generating: _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n};\n// Beautiful gradient colors matching the reference design\nconst STEP_COLORS = {\n    pending: {\n        bg: 'bg-gradient-to-br from-slate-800/95 to-slate-900/95',\n        border: 'border-slate-600/40',\n        icon: 'text-slate-400',\n        text: 'text-slate-200',\n        accent: 'bg-slate-500',\n        shadow: 'shadow-slate-500/10'\n    },\n    in_progress: {\n        bg: 'bg-gradient-to-br from-blue-900/95 to-indigo-900/95',\n        border: 'border-blue-500/50',\n        icon: 'text-blue-300',\n        text: 'text-blue-100',\n        accent: 'bg-blue-500',\n        shadow: 'shadow-blue-500/20'\n    },\n    completed: {\n        bg: 'bg-gradient-to-br from-emerald-900/95 to-green-900/95',\n        border: 'border-emerald-500/50',\n        icon: 'text-emerald-300',\n        text: 'text-emerald-100',\n        accent: 'bg-emerald-500',\n        shadow: 'shadow-emerald-500/20'\n    },\n    error: {\n        bg: 'bg-gradient-to-br from-red-900/95 to-rose-900/95',\n        border: 'border-red-500/50',\n        icon: 'text-red-300',\n        text: 'text-red-100',\n        accent: 'bg-red-500',\n        shadow: 'shadow-red-500/20'\n    }\n};\nfunction OrchestrationProgressCard(param) {\n    let { step, isExpanded = false, onToggleExpand, className = '' } = param;\n    var _step_details, _step_details1, _step_details2;\n    _s();\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const colors = STEP_COLORS[step.status];\n    const IconComponent = step.icon ? STEP_ICONS[step.icon] : _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n    const ChevronIcon = isExpanded ? _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"] : _barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n    // Animate status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressCard.useEffect\": ()=>{\n            if (step.status === 'completed' || step.status === 'in_progress') {\n                setIsAnimating(true);\n                const timer = setTimeout({\n                    \"OrchestrationProgressCard.useEffect.timer\": ()=>setIsAnimating(false)\n                }[\"OrchestrationProgressCard.useEffect.timer\"], 600);\n                return ({\n                    \"OrchestrationProgressCard.useEffect\": ()=>clearTimeout(timer)\n                })[\"OrchestrationProgressCard.useEffect\"];\n            }\n        }\n    }[\"OrchestrationProgressCard.useEffect\"], [\n        step.status\n    ]);\n    const handleToggle = ()=>{\n        var _step_details;\n        if (onToggleExpand && (((_step_details = step.details) === null || _step_details === void 0 ? void 0 : _step_details.length) || step.description)) {\n            onToggleExpand();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4 last:mb-0 transition-all duration-300 \".concat(className),\n        style: {\n            animation: 'cardSlideIn 0.5s ease-out'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\\n          relative rounded-xl border backdrop-blur-sm transition-all duration-500 overflow-hidden\\n          \".concat(colors.bg, \" \").concat(colors.border, \" \").concat(colors.shadow, \"\\n          \").concat(step.status === 'in_progress' ? 'shadow-lg ring-1 ring-blue-500/30' : '', \"\\n          \").concat(step.status === 'completed' ? 'shadow-lg ring-1 ring-emerald-500/30' : '', \"\\n          \").concat(step.status === 'error' ? 'shadow-lg ring-1 ring-red-500/30' : '', \"\\n          \").concat(isAnimating ? 'scale-[1.02] shadow-2xl' : 'scale-100', \"\\n          hover:shadow-xl hover:scale-[1.01] hover:ring-1 hover:ring-white/20\\n        \"),\n            style: {\n                animation: step.status === 'in_progress' ? 'statusGlow 2s ease-in-out infinite' : step.status === 'completed' ? 'completedGlow 1s ease-in-out' : step.status === 'error' ? 'errorGlow 1s ease-in-out' : 'none'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 top-0 bottom-0 w-1.5 \".concat(colors.accent, \" transition-all duration-500\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            flex items-center p-5 pl-7 cursor-pointer transition-all duration-300\\n            \".concat(((_step_details = step.details) === null || _step_details === void 0 ? void 0 : _step_details.length) || step.description ? 'hover:bg-white/5' : '', \"\\n          \"),\n                    onClick: handleToggle,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 mr-4\",\n                            children: step.status === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-full bg-emerald-500 flex items-center justify-center shadow-lg shadow-emerald-500/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-full bg-emerald-400/20 animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this) : step.status === 'in_progress' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center shadow-lg shadow-blue-500/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border-2 border-blue-300 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-1 border border-blue-400 border-t-transparent rounded-full animate-spin\",\n                                        style: {\n                                            animationDirection: 'reverse',\n                                            animationDuration: '2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this) : step.status === 'error' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-full bg-red-500 flex items-center justify-center shadow-lg shadow-red-500/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-full bg-red-400/20 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center shadow-lg shadow-slate-500/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BeakerIcon_BoltIcon_CheckCircleIcon_ChevronDownIcon_ChevronRightIcon_ClockIcon_CogIcon_CpuChipIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_SparklesIcon_UserGroupIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-5 h-5 text-slate-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold \".concat(colors.text, \" transition-colors duration-300 leading-tight\"),\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            step.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm \".concat(colors.text, \" opacity-80 mt-2 leading-relaxed\"),\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    step.timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-end ml-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs \".concat(colors.text, \" opacity-60 font-mono\"),\n                                            children: step.timestamp.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        (((_step_details1 = step.details) === null || _step_details1 === void 0 ? void 0 : _step_details1.length) || step.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 ml-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChevronIcon, {\n                                className: \"w-5 h-5 \".concat(colors.icon, \" transition-all duration-300 opacity-60 hover:opacity-100\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                isExpanded && ((_step_details2 = step.details) === null || _step_details2 === void 0 ? void 0 : _step_details2.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t \".concat(colors.border, \" px-7 py-4 bg-black/10 backdrop-blur-sm\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: step.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 rounded-full \".concat(colors.accent, \" mt-2 flex-shrink-0 shadow-sm\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm \".concat(colors.text, \" opacity-90 leading-relaxed\"),\n                                        children: detail\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, this),\n                step.status === 'in_progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute bottom-0 left-0 right-0 h-1.5 bg-black/20 rounded-b-xl overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full bg-gradient-to-r from-blue-500 via-blue-400 to-blue-500 relative overflow-hidden animate-pulse\",\n                        style: {\n                            width: '75%',\n                            animation: 'progressShimmer 3s linear infinite, progressPulse 2s ease-in-out infinite'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-blue-400/50 to-blue-600/50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressCard.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressCard, \"U13FD0PO4FR4rREA5Sq0cx8yDCA=\");\n_c = OrchestrationProgressCard;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressCard.tsx\n"));

/***/ })

});