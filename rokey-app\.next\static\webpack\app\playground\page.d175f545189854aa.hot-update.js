"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationTracking */ \"(app-pages-browser)/./src/hooks/useOrchestrationTracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        className\n    });\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = (0,_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking)();\n    console.log('🎯 Progress state:', {\n        steps: steps.length,\n        trackingActive,\n        isActive\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.CLASSIFICATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('classification', [\n                    \"Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"Analysis completed successfully\",\n                    \"Ready to select the best experts\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.ROLE_SELECTION,\n                    details: [\n                        \"Selected \".concat(selectedRoles.length, \" AI specialists\"),\n                        \"Each expert has been assigned their optimal tools\",\n                        \"Team assembly complete\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('role_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                const workflowNames = {\n                    'sequential': 'Step-by-step collaboration',\n                    'supervisor': 'Coordinated teamwork',\n                    'hierarchical': 'Multi-level coordination',\n                    'parallel': 'Simultaneous processing'\n                };\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.WORKFLOW_SELECTION,\n                    description: \"\".concat(workflowNames[workflowType] || 'Smart collaboration', \" approach selected\"),\n                    details: [\n                        \"Collaboration strategy: \".concat(workflowNames[workflowType] || workflowType),\n                        \"Optimized for your specific request type\",\n                        \"Team coordination plan established\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('workflow_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('agent_creation', [\n                    \"\".concat(agents.length, \" AI specialists ready to work\"),\n                    \"Each expert configured with optimal settings\",\n                    \"Team assembly complete and ready to collaborate\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_init', [\n                    'Team coordinator assigned and ready',\n                    'Communication channels established',\n                    'Ready to manage collaborative workflow'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    'Work distribution strategy finalized',\n                    'Each specialist knows their role and responsibilities',\n                    'Team ready to begin collaborative work'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    'Final response synthesis completed',\n                    'All specialist insights successfully combined',\n                    'Collaborative work finished successfully'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                completeTracking();\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n                // For testing: simulate a simple progress flow\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'classification',\n                            stepId: 'classification',\n                            title: 'Analyzing request with RouKey AI',\n                            description: 'Understanding your request and requirements...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('classification', [\n                            'Request analysis completed',\n                            'Optimal approach identified',\n                            'Ready to process your request'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 1500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'agent_creation',\n                            stepId: 'agent_creation',\n                            title: 'Preparing AI Specialist',\n                            description: 'Setting up the perfect expert for your task...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 2000);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('agent_creation', [\n                            'AI specialist ready to work',\n                            'Expert configured with optimal settings',\n                            'Processing your request...'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking,\n        trackEvent,\n        completeStep\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", false, function() {\n    return [\n        _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});