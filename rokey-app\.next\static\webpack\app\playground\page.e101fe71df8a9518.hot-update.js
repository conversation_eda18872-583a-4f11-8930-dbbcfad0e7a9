"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationTracking */ \"(app-pages-browser)/./src/hooks/useOrchestrationTracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        className\n    });\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = (0,_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking)();\n    console.log('🎯 Progress state:', {\n        steps: steps.length,\n        trackingActive,\n        isActive\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'classification',\n                    stepId: 'analyzing_request',\n                    title: 'Analyzing request with RouKey AI',\n                    description: 'Understanding your request and requirements...',\n                    details: [\n                        'Processing natural language input',\n                        'Identifying task complexity and scope',\n                        'Determining specialist expertise needed',\n                        'Preparing for role classification'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('analyzing_request', [\n                    \"✅ Request analysis complete\",\n                    \"\\uD83C\\uDFAF Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83D\\uDCCA Classification threshold: \".concat(threshold),\n                    \"\\uD83D\\uDE80 Ready to prepare AI specialists\"\n                ]);\n                // Add detailed role breakdown\n                trackEvent({\n                    type: 'role_selection',\n                    stepId: 'preparing_specialists',\n                    title: 'Preparing AI Specialists',\n                    description: 'Setting up the perfect expert team for your task...',\n                    details: [\n                        \"Selected roles: \".concat(roles.join(', ')),\n                        'Configuring specialist capabilities',\n                        'Assigning API keys to each role',\n                        'Preparing collaborative workflow'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                completeStep('preparing_specialists', [\n                    \"✅ Specialist team configured\",\n                    \"\\uD83D\\uDC65 \".concat(selectedRoles.length, \" experts selected: \").concat(selectedRoles.join(', ')),\n                    \"\\uD83D\\uDD27 API keys assigned and validated\",\n                    \"⚡ Ready for workflow orchestration\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                const workflowNames = {\n                    'sequential': 'Step-by-step collaboration',\n                    'supervisor': 'Coordinated teamwork',\n                    'hierarchical': 'Multi-level coordination',\n                    'parallel': 'Simultaneous processing'\n                };\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.WORKFLOW_SELECTION,\n                    description: \"\".concat(workflowNames[workflowType] || 'Smart collaboration', \" approach selected\"),\n                    details: [\n                        \"Collaboration strategy: \".concat(workflowNames[workflowType] || workflowType),\n                        \"Optimized for your specific request type\",\n                        \"Team coordination plan established\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('workflow_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('agent_creation', [\n                    \"\".concat(agents.length, \" AI specialists ready to work\"),\n                    \"Each expert configured with optimal settings\",\n                    \"Team assembly complete and ready to collaborate\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_init', [\n                    'Team coordinator assigned and ready',\n                    'Communication channels established',\n                    'Ready to manage collaborative workflow'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    'Work distribution strategy finalized',\n                    'Each specialist knows their role and responsibilities',\n                    'Team ready to begin collaborative work'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    'Final response synthesis completed',\n                    'All specialist insights successfully combined',\n                    'Collaborative work finished successfully'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                completeTracking();\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n                // For testing: simulate a simple progress flow\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'classification',\n                            stepId: 'classification',\n                            title: 'Analyzing request with RouKey AI',\n                            description: 'Understanding your request and requirements...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('classification', [\n                            'Request analysis completed',\n                            'Optimal approach identified',\n                            'Ready to process your request'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 1500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'agent_creation',\n                            stepId: 'agent_creation',\n                            title: 'Preparing AI Specialist',\n                            description: 'Setting up the perfect expert for your task...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 2000);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('agent_creation', [\n                            'AI specialist ready to work',\n                            'Expert configured with optimal settings',\n                            'Processing your request...'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking,\n        trackEvent,\n        completeStep\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", false, function() {\n    return [\n        _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});