"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationSSE */ \"(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, sessionId, onProgressCallback, className = '' } = param;\n    _s();\n    console.log('🎯 OrchestrationProgressBridge render:', {\n        isActive,\n        sessionId,\n        className\n    });\n    // Use SSE-based progress tracking\n    const { steps, isActive: sseActive, isComplete, error, connect, disconnect, reset } = (0,_hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE)(sessionId);\n    console.log('🎯 SSE Progress state:', {\n        steps: steps.length,\n        sseActive,\n        isActive,\n        sessionId,\n        isComplete,\n        error\n    });\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'classification',\n                    stepId: 'analyzing_request',\n                    title: 'Analyzing request with RouKey AI',\n                    description: 'Understanding your request and requirements...',\n                    details: [\n                        'Processing natural language input',\n                        'Identifying task complexity and scope',\n                        'Determining specialist expertise needed',\n                        'Preparing for role classification'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('analyzing_request', [\n                    \"✅ Request analysis complete\",\n                    \"\\uD83C\\uDFAF Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"\\uD83D\\uDCCA Classification threshold: \".concat(threshold),\n                    \"\\uD83D\\uDE80 Ready to prepare AI specialists\"\n                ]);\n                // Add detailed role breakdown\n                trackEvent({\n                    type: 'role_selection',\n                    stepId: 'preparing_specialists',\n                    title: 'Preparing AI Specialists',\n                    description: 'Setting up the perfect expert team for your task...',\n                    details: [\n                        \"Selected roles: \".concat(roles.join(', ')),\n                        'Configuring specialist capabilities',\n                        'Assigning API keys to each role',\n                        'Preparing collaborative workflow'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                completeStep('preparing_specialists', [\n                    \"✅ Specialist team configured\",\n                    \"\\uD83D\\uDC65 \".concat(selectedRoles.length, \" experts selected: \").concat(selectedRoles.join(', ')),\n                    \"\\uD83D\\uDD27 API keys assigned and validated\",\n                    \"⚡ Ready for workflow orchestration\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                completeStep('orchestration_planning', [\n                    \"✅ Orchestration strategy finalized\",\n                    \"\\uD83C\\uDFAF Selected approach: \".concat(workflowType.toUpperCase()),\n                    \"\\uD83D\\uDCA1 Strategy: \".concat(reasoning),\n                    \"\\uD83D\\uDE80 Ready to create AI agents\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'agent_creation',\n                    stepId: 'creating_agents',\n                    title: 'Creating AI Agents',\n                    description: 'Initializing specialized AI agents with your API keys...',\n                    details: [\n                        'Configuring agent personalities and capabilities',\n                        'Assigning API keys to each specialist',\n                        'Setting up communication protocols',\n                        'Preparing for collaborative work'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('creating_agents', [\n                    \"✅ \".concat(agents.length, \" AI agents created successfully\"),\n                    \"\\uD83D\\uDD11 API keys: \".concat(agents.map({\n                        \"OrchestrationProgressBridge.useCallback[progressCallback]\": (a)=>\"\".concat(a.role, \" (\").concat(a.apiKey, \")\")\n                    }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]).join(', ')),\n                    \"\\uD83E\\uDD16 All agents ready for collaboration\",\n                    \"⚡ Team assembly complete\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_init',\n                    stepId: 'supervisor_setup',\n                    title: 'Setting Up Team Coordinator',\n                    description: 'Initializing supervisor agent to manage collaboration...',\n                    details: [\n                        'Selecting most capable agent as supervisor',\n                        'Establishing communication protocols',\n                        'Setting up task delegation system',\n                        'Preparing coordination strategies'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_setup', [\n                    \"✅ Team coordinator ready: \".concat(supervisorRole),\n                    \"\\uD83D\\uDCCB Communication channels established\",\n                    \"\\uD83C\\uDFAF Task delegation system active\",\n                    \"⚡ Ready to manage collaborative workflow\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'task_planning',\n                    stepId: 'task_planning',\n                    title: 'Planning Task Distribution',\n                    description: 'Supervisor is planning how to distribute work among specialists...',\n                    details: [\n                        'Analyzing task complexity and requirements',\n                        'Determining optimal work distribution',\n                        'Planning collaboration sequence',\n                        'Preparing task assignments for each agent'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    \"✅ Work distribution strategy finalized\",\n                    \"\\uD83D\\uDCCB Plan: \".concat(plan),\n                    \"\\uD83D\\uDC65 Each specialist knows their responsibilities\",\n                    \"\\uD83D\\uDE80 Team ready to begin collaborative work\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    \"✅ Final response synthesis completed\",\n                    \"\\uD83C\\uDFAF All specialist insights successfully combined\",\n                    \"\\uD83D\\uDCDD Comprehensive response ready\",\n                    \"⚡ Collaborative work finished successfully\"\n                ]);\n                // Add final \"generating response\" step\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        trackEvent({\n                            type: 'orchestration_complete',\n                            stepId: 'generating_response',\n                            title: 'Generating Response',\n                            description: 'Finalizing and streaming your comprehensive response...',\n                            details: [\n                                'Formatting final response',\n                                'Preparing for streaming delivery',\n                                'Quality assurance complete',\n                                'Ready to deliver results'\n                            ]\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 200);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                var _result_metadata, _result_metadata1;\n                completeStep('generating_response', [\n                    \"✅ Response generation complete\",\n                    \"\\uD83D\\uDCCA Total tokens: \".concat(((_result_metadata = result.metadata) === null || _result_metadata === void 0 ? void 0 : _result_metadata.totalTokens) || 'N/A'),\n                    \"⏱️ Execution time: \".concat(((_result_metadata1 = result.metadata) === null || _result_metadata1 === void 0 ? void 0 : _result_metadata1.executionTime) || 'N/A', \"ms\"),\n                    \"\\uD83C\\uDF89 Multi-role orchestration successful!\"\n                ]);\n                // Complete tracking after a short delay to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                        completeTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 2000);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking\n    ]);\n    console.log('🎯 Render decision:', {\n        trackingActive,\n        stepsLength: steps.length,\n        shouldRender: trackingActive && steps.length > 0\n    });\n    if (!trackingActive || steps.length === 0) {\n        console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);\n        return null;\n    }\n    console.log('🎯 Rendering progress tracker with steps:', steps);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 309,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 308,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"MV7b4B3Euz5CPsltLA582nV5DWw=\", false, function() {\n    return [\n        _hooks_useOrchestrationSSE__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationSSE\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts":
/*!******************************************!*\
  !*** ./src/hooks/useOrchestrationSSE.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOrchestrationSSE: () => (/* binding */ useOrchestrationSSE)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useOrchestrationSSE auto */ \nfunction useOrchestrationSSE(sessionId) {\n    const [steps, setSteps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttempts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    const convertToProgressStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[convertToProgressStep]\": (step)=>{\n            return {\n                id: step.id,\n                title: step.title,\n                description: step.description,\n                status: step.status,\n                timestamp: new Date(step.timestamp),\n                details: step.details,\n                metadata: step.metadata,\n                icon: getIconForStep(step.id)\n            };\n        }\n    }[\"useOrchestrationSSE.useCallback[convertToProgressStep]\"], []);\n    const getIconForStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[getIconForStep]\": (stepId)=>{\n            if (stepId.includes('analyzing') || stepId.includes('classification')) return 'analysis';\n            if (stepId.includes('specialist') || stepId.includes('role')) return 'roles';\n            if (stepId.includes('orchestration') || stepId.includes('workflow')) return 'workflow';\n            if (stepId.includes('agent') || stepId.includes('creating')) return 'agents';\n            if (stepId.includes('supervisor')) return 'supervisor';\n            if (stepId.includes('planning') || stepId.includes('task')) return 'planning';\n            if (stepId.includes('working') || stepId.includes('agent_')) return 'working';\n            if (stepId.includes('synthesis') || stepId.includes('combining')) return 'synthesis';\n            if (stepId.includes('generating') || stepId.includes('response')) return 'generating';\n            return 'connecting';\n        }\n    }[\"useOrchestrationSSE.useCallback[getIconForStep]\"], []);\n    const connectToSSE = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[connectToSSE]\": ()=>{\n            if (!sessionId || eventSourceRef.current) return;\n            console.log(\"[SSE] Connecting to orchestration progress: \".concat(sessionId));\n            try {\n                const eventSource = new EventSource(\"/api/orchestration/progress?sessionId=\".concat(sessionId));\n                eventSourceRef.current = eventSource;\n                eventSource.onopen = ({\n                    \"useOrchestrationSSE.useCallback[connectToSSE]\": ()=>{\n                        console.log('[SSE] Connected to orchestration progress stream');\n                        reconnectAttempts.current = 0;\n                        setIsActive(true);\n                        setError(null);\n                    }\n                })[\"useOrchestrationSSE.useCallback[connectToSSE]\"];\n                eventSource.onmessage = ({\n                    \"useOrchestrationSSE.useCallback[connectToSSE]\": (event)=>{\n                        try {\n                            const data = JSON.parse(event.data);\n                            console.log('[SSE] Received progress data:', data);\n                            switch(data.type){\n                                case 'initial':\n                                    if (data.steps) {\n                                        setSteps(data.steps.map(convertToProgressStep));\n                                    }\n                                    if (data.isComplete) {\n                                        setIsComplete(true);\n                                        setIsActive(false);\n                                    }\n                                    if (data.error) {\n                                        setError(data.error);\n                                    }\n                                    break;\n                                case 'step_update':\n                                    if (data.step) {\n                                        const newStep = convertToProgressStep(data.step);\n                                        setSteps({\n                                            \"useOrchestrationSSE.useCallback[connectToSSE]\": (prev)=>{\n                                                const existingIndex = prev.findIndex({\n                                                    \"useOrchestrationSSE.useCallback[connectToSSE].existingIndex\": (s)=>s.id === newStep.id\n                                                }[\"useOrchestrationSSE.useCallback[connectToSSE].existingIndex\"]);\n                                                if (existingIndex >= 0) {\n                                                    // Update existing step\n                                                    const updated = [\n                                                        ...prev\n                                                    ];\n                                                    updated[existingIndex] = newStep;\n                                                    return updated;\n                                                } else {\n                                                    // Add new step\n                                                    return [\n                                                        ...prev,\n                                                        newStep\n                                                    ];\n                                                }\n                                            }\n                                        }[\"useOrchestrationSSE.useCallback[connectToSSE]\"]);\n                                    }\n                                    break;\n                                case 'complete':\n                                    console.log('[SSE] Orchestration completed');\n                                    setIsComplete(true);\n                                    setIsActive(false);\n                                    // Keep connection open briefly to show final state\n                                    setTimeout({\n                                        \"useOrchestrationSSE.useCallback[connectToSSE]\": ()=>{\n                                            eventSource.close();\n                                        }\n                                    }[\"useOrchestrationSSE.useCallback[connectToSSE]\"], 3000);\n                                    break;\n                                case 'error':\n                                    console.error('[SSE] Orchestration error:', data.error);\n                                    setError(data.error || 'Unknown error occurred');\n                                    setIsActive(false);\n                                    break;\n                                case 'heartbeat':\n                                    break;\n                                default:\n                                    console.warn('[SSE] Unknown message type:', data.type);\n                            }\n                        } catch (parseError) {\n                            console.error('[SSE] Error parsing progress data:', parseError);\n                        }\n                    }\n                })[\"useOrchestrationSSE.useCallback[connectToSSE]\"];\n                eventSource.onerror = ({\n                    \"useOrchestrationSSE.useCallback[connectToSSE]\": (error)=>{\n                        console.error('[SSE] Connection error:', error);\n                        eventSource.close();\n                        eventSourceRef.current = null;\n                        // Attempt to reconnect with exponential backoff\n                        if (reconnectAttempts.current < maxReconnectAttempts) {\n                            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 10000);\n                            console.log(\"[SSE] Reconnecting in \".concat(delay, \"ms (attempt \").concat(reconnectAttempts.current + 1, \"/\").concat(maxReconnectAttempts, \")\"));\n                            reconnectTimeoutRef.current = setTimeout({\n                                \"useOrchestrationSSE.useCallback[connectToSSE]\": ()=>{\n                                    reconnectAttempts.current++;\n                                    connectToSSE();\n                                }\n                            }[\"useOrchestrationSSE.useCallback[connectToSSE]\"], delay);\n                        } else {\n                            console.error('[SSE] Max reconnection attempts reached');\n                            setError('Connection lost. Please refresh the page.');\n                            setIsActive(false);\n                        }\n                    }\n                })[\"useOrchestrationSSE.useCallback[connectToSSE]\"];\n            } catch (error) {\n                console.error('[SSE] Failed to create EventSource:', error);\n                setError('Failed to connect to progress stream');\n            }\n        }\n    }[\"useOrchestrationSSE.useCallback[connectToSSE]\"], [\n        sessionId,\n        convertToProgressStep\n    ]);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[disconnect]\": ()=>{\n            if (eventSourceRef.current) {\n                console.log('[SSE] Disconnecting from orchestration progress');\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            setIsActive(false);\n        }\n    }[\"useOrchestrationSSE.useCallback[disconnect]\"], []);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationSSE.useCallback[reset]\": ()=>{\n            disconnect();\n            setSteps([]);\n            setIsComplete(false);\n            setError(null);\n            reconnectAttempts.current = 0;\n        }\n    }[\"useOrchestrationSSE.useCallback[reset]\"], [\n        disconnect\n    ]);\n    // Connect when sessionId is provided\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationSSE.useEffect\": ()=>{\n            if (sessionId) {\n                connectToSSE();\n            } else {\n                disconnect();\n            }\n            return ({\n                \"useOrchestrationSSE.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useOrchestrationSSE.useEffect\"];\n        }\n    }[\"useOrchestrationSSE.useEffect\"], [\n        sessionId,\n        connectToSSE,\n        disconnect\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationSSE.useEffect\": ()=>{\n            return ({\n                \"useOrchestrationSSE.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useOrchestrationSSE.useEffect\"];\n        }\n    }[\"useOrchestrationSSE.useEffect\"], [\n        disconnect\n    ]);\n    return {\n        steps,\n        isActive,\n        isComplete,\n        error,\n        connect: connectToSSE,\n        disconnect,\n        reset\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useOrchestrationSSE.ts\n"));

/***/ })

});