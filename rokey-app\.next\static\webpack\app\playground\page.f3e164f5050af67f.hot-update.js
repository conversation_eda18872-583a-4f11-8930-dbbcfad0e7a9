"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx":
/*!********************************************************!*\
  !*** ./src/components/OrchestrationProgressBridge.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrchestrationProgressBridge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrchestrationProgressTracker */ \"(app-pages-browser)/./src/components/OrchestrationProgressTracker.tsx\");\n/* harmony import */ var _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useOrchestrationTracking */ \"(app-pages-browser)/./src/hooks/useOrchestrationTracking.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OrchestrationProgressBridge(param) {\n    let { isActive, onProgressCallback, className = '' } = param;\n    _s();\n    const { steps, isActive: trackingActive, trackEvent, completeStep, startTracking, completeTracking, resetTracking } = (0,_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking)();\n    // Create progress callback for orchestration system\n    const progressCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        onClassificationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.CLASSIFICATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onClassificationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (roles, threshold)=>{\n                completeStep('classification', [\n                    \"Identified \".concat(roles.length, \" specialist areas needed\"),\n                    \"Analysis completed successfully\",\n                    \"Ready to select the best experts\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onRoleSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (selectedRoles, filteredRoles)=>{\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.ROLE_SELECTION,\n                    details: [\n                        \"Selected \".concat(selectedRoles.length, \" AI specialists\"),\n                        \"Each expert has been assigned their optimal tools\",\n                        \"Team assembly complete\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('role_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onWorkflowSelectionComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (workflowType, reasoning)=>{\n                const workflowNames = {\n                    'sequential': 'Step-by-step collaboration',\n                    'supervisor': 'Coordinated teamwork',\n                    'hierarchical': 'Multi-level coordination',\n                    'parallel': 'Simultaneous processing'\n                };\n                trackEvent({\n                    ..._hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.WORKFLOW_SELECTION,\n                    description: \"\".concat(workflowNames[workflowType] || 'Smart collaboration', \" approach selected\"),\n                    details: [\n                        \"Collaboration strategy: \".concat(workflowNames[workflowType] || workflowType),\n                        \"Optimized for your specific request type\",\n                        \"Team coordination plan established\"\n                    ]\n                });\n                setTimeout({\n                    \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>completeStep('workflow_selection')\n                }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"], 100);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentCreationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (agents)=>{\n                completeStep('agent_creation', [\n                    \"\".concat(agents.length, \" AI specialists ready to work\"),\n                    \"Each expert configured with optimal settings\",\n                    \"Team assembly complete and ready to collaborate\"\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorInitComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (supervisorRole)=>{\n                completeStep('supervisor_init', [\n                    'Team coordinator assigned and ready',\n                    'Communication channels established',\n                    'Ready to manage collaborative workflow'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent(_hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onTaskPlanningComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (plan)=>{\n                completeStep('task_planning', [\n                    'Work distribution strategy finalized',\n                    'Each specialist knows their role and responsibilities',\n                    'Team ready to begin collaborative work'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, task)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                trackEvent({\n                    type: 'agent_working',\n                    stepId: \"agent_\".concat(role),\n                    title: \"\".concat(roleNames[role] || 'AI Specialist', \" Working\"),\n                    description: task,\n                    details: [\n                        \"Specialist: \".concat(roleNames[role] || role),\n                        'Applying expertise to your request',\n                        'Processing and generating response...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onAgentWorkComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (role, result)=>{\n                const roleNames = {\n                    'brainstorming_ideation': 'Creative Ideation Specialist',\n                    'writing': 'Content Writing Expert',\n                    'coding_backend': 'Backend Development Expert',\n                    'coding_frontend': 'Frontend Development Expert',\n                    'general_chat': 'General AI Assistant'\n                };\n                completeStep(\"agent_\".concat(role), [\n                    \"\".concat(roleNames[role] || 'Specialist', \" work completed\"),\n                    \"High-quality response generated\",\n                    'Contribution ready for team integration'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisStart: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": ()=>{\n                trackEvent({\n                    type: 'supervisor_synthesis',\n                    stepId: 'supervisor_synthesis',\n                    title: 'Team Coordinator: Combining Results',\n                    description: 'Creating comprehensive final response...',\n                    details: [\n                        'Reviewing all specialist contributions',\n                        'Combining insights into unified response',\n                        'Ensuring quality and coherence'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onSupervisorSynthesisComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (synthesis)=>{\n                completeStep('supervisor_synthesis', [\n                    'Final response synthesis completed',\n                    'All specialist insights successfully combined',\n                    'Collaborative work finished successfully'\n                ]);\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onOrchestrationComplete: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (result)=>{\n                completeTracking();\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"],\n        onError: {\n            \"OrchestrationProgressBridge.useCallback[progressCallback]\": (step, error)=>{\n                trackEvent({\n                    type: 'error',\n                    stepId: \"error_\".concat(step),\n                    title: \"Error in \".concat(step),\n                    description: error,\n                    details: [\n                        \"Step: \".concat(step),\n                        \"Error: \".concat(error),\n                        'Attempting recovery...'\n                    ]\n                });\n            }\n        }[\"OrchestrationProgressBridge.useCallback[progressCallback]\"]\n    }, [\n        trackEvent,\n        completeStep,\n        completeTracking\n    ]);\n    // Provide progress callback to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (onProgressCallback) {\n                onProgressCallback(progressCallback);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        onProgressCallback,\n        progressCallback\n    ]);\n    // Start/stop tracking based on isActive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationProgressBridge.useEffect\": ()=>{\n            if (isActive && !trackingActive) {\n                startTracking();\n                // For testing: simulate a simple progress flow\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'classification',\n                            stepId: 'classification',\n                            title: 'Analyzing request with RouKey AI',\n                            description: 'Understanding your request and requirements...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('classification', [\n                            'Request analysis completed',\n                            'Optimal approach identified',\n                            'Ready to process your request'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 1500);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        trackEvent({\n                            type: 'agent_creation',\n                            stepId: 'agent_creation',\n                            title: 'Preparing AI Specialist',\n                            description: 'Setting up the perfect expert for your task...'\n                        });\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 2000);\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        completeStep('agent_creation', [\n                            'AI specialist ready to work',\n                            'Expert configured with optimal settings',\n                            'Processing your request...'\n                        ]);\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            } else if (!isActive && trackingActive) {\n                // Small delay before resetting to show final state\n                setTimeout({\n                    \"OrchestrationProgressBridge.useEffect\": ()=>{\n                        resetTracking();\n                    }\n                }[\"OrchestrationProgressBridge.useEffect\"], 3000);\n            }\n        }\n    }[\"OrchestrationProgressBridge.useEffect\"], [\n        isActive,\n        trackingActive,\n        startTracking,\n        resetTracking,\n        trackEvent,\n        completeStep\n    ]);\n    if (!trackingActive || steps.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationProgressTracker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            steps: steps,\n            isActive: trackingActive,\n            autoScroll: true\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationProgressBridge.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(OrchestrationProgressBridge, \"9Ft81T2j+hpuGiygmzSVRKcS9fU=\", false, function() {\n    return [\n        _hooks_useOrchestrationTracking__WEBPACK_IMPORTED_MODULE_3__.useOrchestrationTracking\n    ];\n});\n_c = OrchestrationProgressBridge;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationProgressBridge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationProgressBridge.tsx\n"));

/***/ })

});