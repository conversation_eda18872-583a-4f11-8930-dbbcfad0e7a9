import { NextRequest, NextResponse } from 'next/server';

// Global progress tracking for orchestration sessions
const orchestrationProgress = new Map<string, {
  steps: Array<{
    id: string;
    title: string;
    description?: string;
    status: 'pending' | 'in_progress' | 'completed' | 'error';
    timestamp: Date;
    details?: string[];
  }>;
  isComplete: boolean;
  error?: string;
}>();

// Global event emitter for progress updates
const progressListeners = new Map<string, Array<(data: any) => void>>();

export function updateOrchestrationProgress(sessionId: string, step: any) {
  const session = orchestrationProgress.get(sessionId) || { steps: [], isComplete: false };
  
  // Update or add step
  const existingIndex = session.steps.findIndex(s => s.id === step.id);
  if (existingIndex >= 0) {
    session.steps[existingIndex] = { ...session.steps[existingIndex], ...step };
  } else {
    session.steps.push(step);
  }
  
  orchestrationProgress.set(sessionId, session);
  
  // Notify all listeners for this session
  const listeners = progressListeners.get(sessionId) || [];
  listeners.forEach(listener => {
    try {
      listener({ type: 'step_update', step, steps: session.steps });
    } catch (error) {
      console.error('Error notifying progress listener:', error);
    }
  });
}

export function completeOrchestration(sessionId: string, result?: any) {
  const session = orchestrationProgress.get(sessionId) || { steps: [], isComplete: false };
  session.isComplete = true;
  orchestrationProgress.set(sessionId, session);
  
  // Notify all listeners
  const listeners = progressListeners.get(sessionId) || [];
  listeners.forEach(listener => {
    try {
      listener({ type: 'complete', result, steps: session.steps });
    } catch (error) {
      console.error('Error notifying completion listener:', error);
    }
  });
  
  // Clean up after a delay
  setTimeout(() => {
    orchestrationProgress.delete(sessionId);
    progressListeners.delete(sessionId);
  }, 30000); // 30 seconds
}

export function errorOrchestration(sessionId: string, error: string) {
  const session = orchestrationProgress.get(sessionId) || { steps: [], isComplete: false };
  session.error = error;
  orchestrationProgress.set(sessionId, session);
  
  // Notify all listeners
  const listeners = progressListeners.get(sessionId) || [];
  listeners.forEach(listener => {
    try {
      listener({ type: 'error', error, steps: session.steps });
    } catch (error) {
      console.error('Error notifying error listener:', error);
    }
  });
}

// GET /api/orchestration/progress?sessionId=<ID>
// Server-Sent Events endpoint for real-time progress updates
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const sessionId = searchParams.get('sessionId');
  
  if (!sessionId) {
    return NextResponse.json({ error: 'sessionId is required' }, { status: 400 });
  }
  
  console.log(`[Orchestration Progress] Starting SSE stream for session: ${sessionId}`);
  
  // Create a readable stream for Server-Sent Events
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      
      // Send initial state if available
      const session = orchestrationProgress.get(sessionId);
      if (session) {
        const data = JSON.stringify({
          type: 'initial',
          steps: session.steps,
          isComplete: session.isComplete,
          error: session.error
        });
        controller.enqueue(encoder.encode(`data: ${data}\n\n`));
      }
      
      // Create listener for this session
      const listener = (data: any) => {
        try {
          const eventData = JSON.stringify(data);
          controller.enqueue(encoder.encode(`data: ${eventData}\n\n`));
        } catch (error) {
          console.error('Error sending SSE data:', error);
        }
      };
      
      // Register listener
      if (!progressListeners.has(sessionId)) {
        progressListeners.set(sessionId, []);
      }
      progressListeners.get(sessionId)!.push(listener);
      
      // Send heartbeat every 30 seconds
      const heartbeat = setInterval(() => {
        try {
          controller.enqueue(encoder.encode(`data: ${JSON.stringify({ type: 'heartbeat' })}\n\n`));
        } catch (error) {
          clearInterval(heartbeat);
        }
      }, 30000);
      
      // Clean up on close
      request.signal.addEventListener('abort', () => {
        clearInterval(heartbeat);
        const listeners = progressListeners.get(sessionId) || [];
        const index = listeners.indexOf(listener);
        if (index >= 0) {
          listeners.splice(index, 1);
        }
        if (listeners.length === 0) {
          progressListeners.delete(sessionId);
        }
        controller.close();
      });
    }
  });
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  });
}
