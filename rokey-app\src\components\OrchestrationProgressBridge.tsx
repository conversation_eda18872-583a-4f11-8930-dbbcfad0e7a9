'use client';

import React, { useEffect, useCallback } from 'react';
import OrchestrationProgressTracker from './OrchestrationProgressTracker';
import { useOrchestrationSSE } from '@/hooks/useOrchestrationSSE';
import { type ProgressCallback } from '@/lib/langgraph-orchestration/RouKeyLangGraphIntegration';

interface OrchestrationProgressBridgeProps {
  isActive: boolean;
  sessionId?: string | null;
  onProgressCallback?: (callback: ProgressCallback) => void;
  className?: string;
}

export default function OrchestrationProgressBridge({
  isActive,
  sessionId,
  onProgressCallback,
  className = ''
}: OrchestrationProgressBridgeProps) {
  console.log('🎯 OrchestrationProgressBridge render:', { isActive, sessionId, className });

  // Use SSE-based progress tracking
  const {
    steps,
    isActive: sseActive,
    isComplete,
    error,
    connect,
    disconnect,
    reset
  } = useOrchestrationSSE(sessionId);

  console.log('🎯 SSE Progress state:', {
    steps: steps.length,
    sseActive,
    isActive,
    sessionId,
    isComplete,
    error
  });

  // Create a dummy progress callback for backward compatibility
  // The real progress tracking now happens via SSE
  const progressCallback: ProgressCallback = useCallback({
    onClassificationStart: () => {
      console.log('[Progress Bridge] Classification started (SSE mode)');
    },

    onClassificationComplete: (roles: string[], threshold: number) => {
      console.log('[Progress Bridge] Classification complete (SSE mode):', roles);
    },

    onRoleSelectionComplete: (selectedRoles: string[], filteredRoles: string[]) => {
      console.log('[Progress Bridge] Role selection complete (SSE mode):', selectedRoles);
    },

    onWorkflowSelectionComplete: (workflowType: string, reasoning: string) => {
      console.log('[Progress Bridge] Workflow selection complete (SSE mode):', workflowType);
    },

    onAgentCreationStart: () => {
      console.log('[Progress Bridge] Agent creation started (SSE mode)');
    },

    onAgentCreationComplete: (agents: Array<{ role: string, apiKey: string }>) => {
      console.log('[Progress Bridge] Agent creation complete (SSE mode):', agents.length);
    },

    onSupervisorInitStart: () => {
      console.log('[Progress Bridge] Supervisor init started (SSE mode)');
    },

    onSupervisorInitComplete: (supervisorRole: string) => {
      console.log('[Progress Bridge] Supervisor init complete (SSE mode):', supervisorRole);
    },

    onTaskPlanningStart: () => {
      console.log('[Progress Bridge] Task planning started (SSE mode)');
    },

    onTaskPlanningComplete: (plan: string) => {
      console.log('[Progress Bridge] Task planning complete (SSE mode)');
    },

    onAgentWorkStart: (role: string, task: string) => {
      console.log('[Progress Bridge] Agent work started (SSE mode):', role);
    },

    onAgentWorkComplete: (role: string, result: string) => {
      console.log('[Progress Bridge] Agent work complete (SSE mode):', role);
    },

    onSupervisorSynthesisStart: () => {
      console.log('[Progress Bridge] Supervisor synthesis started (SSE mode)');
    },

    onSupervisorSynthesisComplete: (synthesis: string) => {
      console.log('[Progress Bridge] Supervisor synthesis complete (SSE mode)');
    },

    onOrchestrationComplete: (result: any) => {
      console.log('[Progress Bridge] Orchestration complete (SSE mode)');
    },

    onError: (step: string, error: string) => {
      console.log('[Progress Bridge] Error (SSE mode):', step, error);
    }
  }, []);

  // Provide progress callback to parent for backward compatibility
  useEffect(() => {
    if (onProgressCallback) {
      onProgressCallback(progressCallback);
    }
  }, [onProgressCallback, progressCallback]);

  // Reset SSE connection when not active
  useEffect(() => {
    if (!isActive && !isComplete) {
      reset();
    }
  }, [isActive, isComplete, reset]);

  console.log('🎯 Render decision:', {
    sseActive,
    stepsLength: steps.length,
    isActive,
    sessionId,
    shouldRender: (sseActive || steps.length > 0) && sessionId
  });

  // Only render if we have a session ID and either SSE is active or we have steps to show
  if (!sessionId || (!sseActive && steps.length === 0)) {
    console.log('🎯 Not rendering - sessionId:', sessionId, 'sseActive:', sseActive, 'steps:', steps.length);
    return null;
  }

  console.log('🎯 Rendering SSE progress tracker with steps:', steps);

  return (
    <div className={className}>
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">
            Progress tracking error: {error}
          </p>
        </div>
      )}
      <OrchestrationProgressTracker
        steps={steps}
        isActive={sseActive}
        autoScroll={true}
      />
    </div>
  );
}
