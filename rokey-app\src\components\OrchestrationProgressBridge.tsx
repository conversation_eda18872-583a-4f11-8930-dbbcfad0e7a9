'use client';

import React, { useEffect, useCallback } from 'react';
import OrchestrationProgressTracker from './OrchestrationProgressTracker';
import { useOrchestrationTracking, ORCHESTRATION_STEPS } from '@/hooks/useOrchestrationTracking';
import { type ProgressCallback } from '@/lib/langgraph-orchestration/RouKeyLangGraphIntegration';

interface OrchestrationProgressBridgeProps {
  isActive: boolean;
  onProgressCallback?: (callback: ProgressCallback) => void;
  className?: string;
}

export default function OrchestrationProgressBridge({
  isActive,
  onProgressCallback,
  className = ''
}: OrchestrationProgressBridgeProps) {
  console.log('🎯 OrchestrationProgressBridge render:', { isActive, className });

  const {
    steps,
    isActive: trackingActive,
    trackEvent,
    completeStep,
    startTracking,
    completeTracking,
    resetTracking
  } = useOrchestrationTracking();

  console.log('🎯 Progress state:', { steps: steps.length, trackingActive, isActive });

  // Create progress callback for orchestration system
  const progressCallback: ProgressCallback = useCallback({
    onClassificationStart: () => {
      trackEvent({
        type: 'classification',
        stepId: 'analyzing_request',
        title: 'Analyzing request with RouKey AI',
        description: 'Understanding your request and requirements...',
        details: [
          'Processing natural language input',
          'Identifying task complexity and scope',
          'Determining specialist expertise needed',
          'Preparing for role classification'
        ]
      });
    },

    onClassificationComplete: (roles: string[], threshold: number) => {
      completeStep('analyzing_request', [
        `✅ Request analysis complete`,
        `🎯 Identified ${roles.length} specialist areas needed`,
        `📊 Classification threshold: ${threshold}`,
        `🚀 Ready to prepare AI specialists`
      ]);

      // Add detailed role breakdown
      trackEvent({
        type: 'role_selection',
        stepId: 'preparing_specialists',
        title: 'Preparing AI Specialists',
        description: 'Setting up the perfect expert team for your task...',
        details: [
          `Selected roles: ${roles.join(', ')}`,
          'Configuring specialist capabilities',
          'Assigning API keys to each role',
          'Preparing collaborative workflow'
        ]
      });
    },
    
    onRoleSelectionComplete: (selectedRoles: string[], filteredRoles: string[]) => {
      completeStep('preparing_specialists', [
        `✅ Specialist team configured`,
        `👥 ${selectedRoles.length} experts selected: ${selectedRoles.join(', ')}`,
        `🔧 API keys assigned and validated`,
        `⚡ Ready for workflow orchestration`
      ]);
    },
    
    onWorkflowSelectionComplete: (workflowType: string, reasoning: string) => {
      completeStep('orchestration_planning', [
        `✅ Orchestration strategy finalized`,
        `🎯 Selected approach: ${workflowType.toUpperCase()}`,
        `💡 Strategy: ${reasoning}`,
        `🚀 Ready to create AI agents`
      ]);
    },
    
    onAgentCreationStart: () => {
      trackEvent({
        type: 'agent_creation',
        stepId: 'creating_agents',
        title: 'Creating AI Agents',
        description: 'Initializing specialized AI agents with your API keys...',
        details: [
          'Configuring agent personalities and capabilities',
          'Assigning API keys to each specialist',
          'Setting up communication protocols',
          'Preparing for collaborative work'
        ]
      });
    },

    onAgentCreationComplete: (agents: Array<{ role: string, apiKey: string }>) => {
      completeStep('creating_agents', [
        `✅ ${agents.length} AI agents created successfully`,
        `🔑 API keys: ${agents.map(a => `${a.role} (${a.apiKey})`).join(', ')}`,
        `🤖 All agents ready for collaboration`,
        `⚡ Team assembly complete`
      ]);
    },
    
    onSupervisorInitStart: () => {
      trackEvent({
        type: 'supervisor_init',
        stepId: 'supervisor_setup',
        title: 'Setting Up Team Coordinator',
        description: 'Initializing supervisor agent to manage collaboration...',
        details: [
          'Selecting most capable agent as supervisor',
          'Establishing communication protocols',
          'Setting up task delegation system',
          'Preparing coordination strategies'
        ]
      });
    },

    onSupervisorInitComplete: (supervisorRole: string) => {
      completeStep('supervisor_setup', [
        `✅ Team coordinator ready: ${supervisorRole}`,
        `📋 Communication channels established`,
        `🎯 Task delegation system active`,
        `⚡ Ready to manage collaborative workflow`
      ]);
    },
    
    onTaskPlanningStart: () => {
      trackEvent({
        type: 'task_planning',
        stepId: 'task_planning',
        title: 'Planning Task Distribution',
        description: 'Supervisor is planning how to distribute work among specialists...',
        details: [
          'Analyzing task complexity and requirements',
          'Determining optimal work distribution',
          'Planning collaboration sequence',
          'Preparing task assignments for each agent'
        ]
      });
    },

    onTaskPlanningComplete: (plan: string) => {
      completeStep('task_planning', [
        `✅ Work distribution strategy finalized`,
        `📋 Plan: ${plan}`,
        `👥 Each specialist knows their responsibilities`,
        `🚀 Team ready to begin collaborative work`
      ]);
    },
    
    onAgentWorkStart: (role: string, task: string) => {
      const roleNames = {
        'brainstorming_ideation': 'Creative Ideation Specialist',
        'writing': 'Content Writing Expert',
        'coding_backend': 'Backend Development Expert',
        'coding_frontend': 'Frontend Development Expert',
        'general_chat': 'General AI Assistant'
      };
      
      trackEvent({
        type: 'agent_working',
        stepId: `agent_${role}`,
        title: `${roleNames[role as keyof typeof roleNames] || 'AI Specialist'} Working`,
        description: task,
        details: [
          `Specialist: ${roleNames[role as keyof typeof roleNames] || role}`,
          'Applying expertise to your request',
          'Processing and generating response...'
        ]
      });
    },
    
    onAgentWorkComplete: (role: string, result: string) => {
      const roleNames = {
        'brainstorming_ideation': 'Creative Ideation Specialist',
        'writing': 'Content Writing Expert',
        'coding_backend': 'Backend Development Expert',
        'coding_frontend': 'Frontend Development Expert',
        'general_chat': 'General AI Assistant'
      };
      
      completeStep(`agent_${role}`, [
        `${roleNames[role as keyof typeof roleNames] || 'Specialist'} work completed`,
        `High-quality response generated`,
        'Contribution ready for team integration'
      ]);
    },
    
    onSupervisorSynthesisStart: () => {
      trackEvent({
        type: 'supervisor_synthesis',
        stepId: 'supervisor_synthesis',
        title: 'Team Coordinator: Combining Results',
        description: 'Creating comprehensive final response...',
        details: [
          'Reviewing all specialist contributions',
          'Combining insights into unified response',
          'Ensuring quality and coherence'
        ]
      });
    },
    
    onSupervisorSynthesisComplete: (synthesis: string) => {
      completeStep('supervisor_synthesis', [
        `✅ Final response synthesis completed`,
        `🎯 All specialist insights successfully combined`,
        `📝 Comprehensive response ready`,
        `⚡ Collaborative work finished successfully`
      ]);

      // Add final "generating response" step
      setTimeout(() => {
        trackEvent({
          type: 'orchestration_complete',
          stepId: 'generating_response',
          title: 'Generating Response',
          description: 'Finalizing and streaming your comprehensive response...',
          details: [
            'Formatting final response',
            'Preparing for streaming delivery',
            'Quality assurance complete',
            'Ready to deliver results'
          ]
        });
      }, 200);
    },

    onOrchestrationComplete: (result: any) => {
      completeStep('generating_response', [
        `✅ Response generation complete`,
        `📊 Total tokens: ${result.metadata?.totalTokens || 'N/A'}`,
        `⏱️ Execution time: ${result.metadata?.executionTime || 'N/A'}ms`,
        `🎉 Multi-role orchestration successful!`
      ]);

      // Complete tracking after a short delay to show final state
      setTimeout(() => {
        completeTracking();
      }, 2000);
    },
    
    onError: (step: string, error: string) => {
      trackEvent({
        type: 'error',
        stepId: `error_${step}`,
        title: `Error in ${step}`,
        description: error,
        details: [
          `Step: ${step}`,
          `Error: ${error}`,
          'Attempting recovery...'
        ]
      });
    }
  }, [trackEvent, completeStep, completeTracking]);

  // Provide progress callback to parent
  useEffect(() => {
    if (onProgressCallback) {
      onProgressCallback(progressCallback);
    }
  }, [onProgressCallback, progressCallback]);

  // Start/stop tracking based on isActive
  useEffect(() => {
    if (isActive && !trackingActive) {
      startTracking();
    } else if (!isActive && trackingActive) {
      // Small delay before resetting to show final state
      setTimeout(() => {
        resetTracking();
      }, 3000);
    }
  }, [isActive, trackingActive, startTracking, resetTracking]);

  console.log('🎯 Render decision:', { trackingActive, stepsLength: steps.length, shouldRender: trackingActive && steps.length > 0 });

  if (!trackingActive || steps.length === 0) {
    console.log('🎯 Not rendering - trackingActive:', trackingActive, 'steps:', steps.length);
    return null;
  }

  console.log('🎯 Rendering progress tracker with steps:', steps);

  return (
    <div className={className}>
      <OrchestrationProgressTracker
        steps={steps}
        isActive={trackingActive}
        autoScroll={true}
      />
    </div>
  );
}
