'use client';

import React, { useEffect, useCallback } from 'react';
import OrchestrationProgressTracker from './OrchestrationProgressTracker';
import { useOrchestrationTracking, ORCHESTRATION_STEPS } from '@/hooks/useOrchestrationTracking';
import { type ProgressCallback } from '@/lib/langgraph-orchestration/RouKeyLangGraphIntegration';

interface OrchestrationProgressBridgeProps {
  isActive: boolean;
  onProgressCallback?: (callback: ProgressCallback) => void;
  className?: string;
}

export default function OrchestrationProgressBridge({
  isActive,
  onProgressCallback,
  className = ''
}: OrchestrationProgressBridgeProps) {
  const {
    steps,
    isActive: trackingActive,
    trackEvent,
    completeStep,
    startTracking,
    completeTracking,
    resetTracking
  } = useOrchestrationTracking();

  // Create progress callback for orchestration system
  const progressCallback: ProgressCallback = useCallback({
    onClassificationStart: () => {
      trackEvent(ORCHESTRATION_STEPS.MULTI_ROLE.CLASSIFICATION);
    },
    
    onClassificationComplete: (roles: string[], threshold: number) => {
      completeStep('classification', [
        `Identified ${roles.length} specialist areas needed`,
        `Analysis completed successfully`,
        `Ready to select the best experts`
      ]);
    },
    
    onRoleSelectionComplete: (selectedRoles: string[], filteredRoles: string[]) => {
      trackEvent({
        ...ORCHESTRATION_STEPS.MULTI_ROLE.ROLE_SELECTION,
        details: [
          `Selected ${selectedRoles.length} AI specialists`,
          `Each expert has been assigned their optimal tools`,
          `Team assembly complete`
        ]
      });
      setTimeout(() => completeStep('role_selection'), 100);
    },
    
    onWorkflowSelectionComplete: (workflowType: string, reasoning: string) => {
      const workflowNames = {
        'sequential': 'Step-by-step collaboration',
        'supervisor': 'Coordinated teamwork',
        'hierarchical': 'Multi-level coordination',
        'parallel': 'Simultaneous processing'
      };
      
      trackEvent({
        ...ORCHESTRATION_STEPS.MULTI_ROLE.WORKFLOW_SELECTION,
        description: `${workflowNames[workflowType as keyof typeof workflowNames] || 'Smart collaboration'} approach selected`,
        details: [
          `Collaboration strategy: ${workflowNames[workflowType as keyof typeof workflowNames] || workflowType}`,
          `Optimized for your specific request type`,
          `Team coordination plan established`
        ]
      });
      setTimeout(() => completeStep('workflow_selection'), 100);
    },
    
    onAgentCreationStart: () => {
      trackEvent(ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);
    },
    
    onAgentCreationComplete: (agents: Array<{ role: string, apiKey: string }>) => {
      completeStep('agent_creation', [
        `${agents.length} AI specialists ready to work`,
        `Each expert configured with optimal settings`,
        `Team assembly complete and ready to collaborate`
      ]);
    },
    
    onSupervisorInitStart: () => {
      trackEvent(ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);
    },
    
    onSupervisorInitComplete: (supervisorRole: string) => {
      completeStep('supervisor_init', [
        'Team coordinator assigned and ready',
        'Communication channels established',
        'Ready to manage collaborative workflow'
      ]);
    },
    
    onTaskPlanningStart: () => {
      trackEvent(ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);
    },
    
    onTaskPlanningComplete: (plan: string) => {
      completeStep('task_planning', [
        'Work distribution strategy finalized',
        'Each specialist knows their role and responsibilities',
        'Team ready to begin collaborative work'
      ]);
    },
    
    onAgentWorkStart: (role: string, task: string) => {
      const roleNames = {
        'brainstorming_ideation': 'Creative Ideation Specialist',
        'writing': 'Content Writing Expert',
        'coding_backend': 'Backend Development Expert',
        'coding_frontend': 'Frontend Development Expert',
        'general_chat': 'General AI Assistant'
      };
      
      trackEvent({
        type: 'agent_working',
        stepId: `agent_${role}`,
        title: `${roleNames[role as keyof typeof roleNames] || 'AI Specialist'} Working`,
        description: task,
        details: [
          `Specialist: ${roleNames[role as keyof typeof roleNames] || role}`,
          'Applying expertise to your request',
          'Processing and generating response...'
        ]
      });
    },
    
    onAgentWorkComplete: (role: string, result: string) => {
      const roleNames = {
        'brainstorming_ideation': 'Creative Ideation Specialist',
        'writing': 'Content Writing Expert',
        'coding_backend': 'Backend Development Expert',
        'coding_frontend': 'Frontend Development Expert',
        'general_chat': 'General AI Assistant'
      };
      
      completeStep(`agent_${role}`, [
        `${roleNames[role as keyof typeof roleNames] || 'Specialist'} work completed`,
        `High-quality response generated`,
        'Contribution ready for team integration'
      ]);
    },
    
    onSupervisorSynthesisStart: () => {
      trackEvent({
        type: 'supervisor_synthesis',
        stepId: 'supervisor_synthesis',
        title: 'Team Coordinator: Combining Results',
        description: 'Creating comprehensive final response...',
        details: [
          'Reviewing all specialist contributions',
          'Combining insights into unified response',
          'Ensuring quality and coherence'
        ]
      });
    },
    
    onSupervisorSynthesisComplete: (synthesis: string) => {
      completeStep('supervisor_synthesis', [
        'Final response synthesis completed',
        'All specialist insights successfully combined',
        'Collaborative work finished successfully'
      ]);
    },
    
    onOrchestrationComplete: (result: any) => {
      completeTracking();
    },
    
    onError: (step: string, error: string) => {
      trackEvent({
        type: 'error',
        stepId: `error_${step}`,
        title: `Error in ${step}`,
        description: error,
        details: [
          `Step: ${step}`,
          `Error: ${error}`,
          'Attempting recovery...'
        ]
      });
    }
  }, [trackEvent, completeStep, completeTracking]);

  // Provide progress callback to parent
  useEffect(() => {
    if (onProgressCallback) {
      onProgressCallback(progressCallback);
    }
  }, [onProgressCallback, progressCallback]);

  // Start/stop tracking based on isActive
  useEffect(() => {
    if (isActive && !trackingActive) {
      startTracking();
    } else if (!isActive && trackingActive) {
      // Small delay before resetting to show final state
      setTimeout(() => {
        resetTracking();
      }, 3000);
    }
  }, [isActive, trackingActive, startTracking, resetTracking]);

  if (!trackingActive || steps.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      <OrchestrationProgressTracker
        steps={steps}
        isActive={trackingActive}
        autoScroll={true}
      />
    </div>
  );
}
