'use client';

import React, { useState, useEffect } from 'react';
import {
  ChevronDownIcon,
  ChevronRightIcon,
  CheckCircleIcon,
  CogIcon,
  SparklesIcon,
  UserGroupIcon,
  BoltIcon,
  PencilIcon,
  BeakerIcon,
  RocketLaunchIcon,
  CommandLineIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  CpuChipIcon,
  LightBulbIcon,
  FireIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export interface ProgressStep {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  timestamp?: Date;
  details?: string[];
  metadata?: Record<string, any>;
  icon?: 'analysis' | 'roles' | 'workflow' | 'agents' | 'supervisor' | 'planning' | 'working' | 'synthesis' | 'connecting' | 'generating';
}

const STEP_ICONS = {
  analysis: MagnifyingGlassIcon,
  roles: UserGroupIcon,
  workflow: ArrowPathIcon,
  agents: <PERSON><PERSON>Chip<PERSON><PERSON>,
  supervisor: <PERSON><PERSON>lesI<PERSON>,
  planning: LightBulbIcon,
  working: CogIcon,
  synthesis: BeakerIcon,
  connecting: BoltIcon,
  generating: PencilIcon
};

// Beautiful gradient colors matching the reference design
const STEP_COLORS = {
  pending: {
    bg: 'bg-gradient-to-br from-slate-800/95 to-slate-900/95',
    border: 'border-slate-600/40',
    icon: 'text-slate-400',
    text: 'text-slate-200',
    accent: 'bg-slate-500',
    shadow: 'shadow-slate-500/10'
  },
  in_progress: {
    bg: 'bg-gradient-to-br from-blue-900/95 to-indigo-900/95',
    border: 'border-blue-500/50',
    icon: 'text-blue-300',
    text: 'text-blue-100',
    accent: 'bg-blue-500',
    shadow: 'shadow-blue-500/20'
  },
  completed: {
    bg: 'bg-gradient-to-br from-emerald-900/95 to-green-900/95',
    border: 'border-emerald-500/50',
    icon: 'text-emerald-300',
    text: 'text-emerald-100',
    accent: 'bg-emerald-500',
    shadow: 'shadow-emerald-500/20'
  },
  error: {
    bg: 'bg-gradient-to-br from-red-900/95 to-rose-900/95',
    border: 'border-red-500/50',
    icon: 'text-red-300',
    text: 'text-red-100',
    accent: 'bg-red-500',
    shadow: 'shadow-red-500/20'
  }
};

interface OrchestrationProgressCardProps {
  step: ProgressStep;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  className?: string;
}

export default function OrchestrationProgressCard({
  step,
  isExpanded = false,
  onToggleExpand,
  className = ''
}: OrchestrationProgressCardProps) {
  const [isAnimating, setIsAnimating] = useState(false);
  
  const colors = STEP_COLORS[step.status];
  const IconComponent = step.icon ? STEP_ICONS[step.icon] : CogIcon;
  const ChevronIcon = isExpanded ? ChevronDownIcon : ChevronRightIcon;

  // Animate status changes
  useEffect(() => {
    if (step.status === 'completed' || step.status === 'in_progress') {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 600);
      return () => clearTimeout(timer);
    }
  }, [step.status]);

  const handleToggle = () => {
    if (onToggleExpand && (step.details?.length || step.description)) {
      onToggleExpand();
    }
  };

  return (
    <div className={`mb-4 last:mb-0 transition-all duration-300 ${className}`} style={{ animation: 'cardSlideIn 0.5s ease-out' }}>
      <div
        className={`
          relative rounded-xl border backdrop-blur-sm transition-all duration-500 overflow-hidden
          ${colors.bg} ${colors.border} ${colors.shadow}
          ${step.status === 'in_progress' ? 'shadow-lg ring-1 ring-blue-500/30' : ''}
          ${step.status === 'completed' ? 'shadow-lg ring-1 ring-emerald-500/30' : ''}
          ${step.status === 'error' ? 'shadow-lg ring-1 ring-red-500/30' : ''}
          ${isAnimating ? 'scale-[1.02] shadow-2xl' : 'scale-100'}
          hover:shadow-xl hover:scale-[1.01] hover:ring-1 hover:ring-white/20
        `}
        style={{
          animation: step.status === 'in_progress' ? 'statusGlow 2s ease-in-out infinite' :
                    step.status === 'completed' ? 'completedGlow 1s ease-in-out' :
                    step.status === 'error' ? 'errorGlow 1s ease-in-out' : 'none'
        }}
      >
        {/* Left accent bar - thicker and more prominent */}
        <div className={`absolute left-0 top-0 bottom-0 w-1.5 ${colors.accent} transition-all duration-500`} />
        {/* Main card content */}
        <div
          className={`
            flex items-center p-5 pl-7 cursor-pointer transition-all duration-300
            ${(step.details?.length || step.description) ? 'hover:bg-white/5' : ''}
          `}
          onClick={handleToggle}
        >
          {/* Beautiful status icon with proper styling */}
          <div className="flex-shrink-0 mr-4">
            {step.status === 'completed' ? (
              <div className="relative" style={{ animation: 'iconPulse 0.6s ease-out' }}>
                <div className="w-8 h-8 rounded-full bg-emerald-500 flex items-center justify-center shadow-lg shadow-emerald-500/30">
                  <CheckCircleIcon className="w-5 h-5 text-white" />
                </div>
                <div className="absolute inset-0 rounded-full bg-emerald-400/20 animate-ping" />
              </div>
            ) : step.status === 'in_progress' ? (
              <div className="relative">
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center shadow-lg shadow-blue-500/30" style={{ animation: 'iconPulse 2s ease-in-out infinite' }}>
                  <IconComponent className="w-5 h-5 text-white" />
                </div>
                <div className="absolute inset-0 border-2 border-blue-300 border-t-transparent rounded-full animate-spin" />
                <div className="absolute inset-1 border border-blue-400 border-t-transparent rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '2s' }} />
              </div>
            ) : step.status === 'error' ? (
              <div className="relative" style={{ animation: 'iconPulse 0.6s ease-out' }}>
                <div className="w-8 h-8 rounded-full bg-red-500 flex items-center justify-center shadow-lg shadow-red-500/30">
                  <XCircleIcon className="w-5 h-5 text-white" />
                </div>
                <div className="absolute inset-0 rounded-full bg-red-400/20 animate-pulse" />
              </div>
            ) : (
              <div className="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center shadow-lg shadow-slate-500/20">
                <ClockIcon className="w-5 h-5 text-slate-300" />
              </div>
            )}
          </div>

          {/* Content with better typography */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className={`text-base font-semibold ${colors.text} transition-colors duration-300 leading-tight`}>
                  {step.title}
                </h3>
                {/* Description with better styling */}
                {step.description && (
                  <p className={`text-sm ${colors.text} opacity-80 mt-2 leading-relaxed`}>
                    {step.description}
                  </p>
                )}
              </div>

              {/* Timestamp with better positioning */}
              {step.timestamp && (
                <div className="flex flex-col items-end ml-4">
                  <span className={`text-xs ${colors.text} opacity-60 font-mono`}>
                    {step.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Expand chevron with better styling */}
          {(step.details?.length || step.description) && (
            <div className="flex-shrink-0 ml-3">
              <ChevronIcon className={`w-5 h-5 ${colors.icon} transition-all duration-300 opacity-60 hover:opacity-100`} />
            </div>
          )}
        </div>

        {/* Beautiful expandable details */}
        {isExpanded && step.details?.length && (
          <div className={`border-t ${colors.border} px-7 py-4 bg-black/10 backdrop-blur-sm`}>
            <div className="space-y-3">
              {step.details.map((detail, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`w-2 h-2 rounded-full ${colors.accent} mt-2 flex-shrink-0 shadow-sm`} />
                  <p className={`text-sm ${colors.text} opacity-90 leading-relaxed`}>{detail}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Beautiful animated progress bar for in-progress items */}
        {step.status === 'in_progress' && (
          <div className="absolute bottom-0 left-0 right-0 h-1.5 bg-black/20 rounded-b-xl overflow-hidden">
            <div className="h-full bg-gradient-to-r from-blue-500 via-blue-400 to-blue-500 relative overflow-hidden animate-pulse"
                 style={{
                   width: '75%',
                   animation: 'progressShimmer 3s linear infinite, progressPulse 2s ease-in-out infinite'
                 }}>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse" />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/50 to-blue-600/50" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
