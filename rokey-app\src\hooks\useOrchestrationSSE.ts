'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { ProgressStep } from '@/components/OrchestrationProgressCard';

interface SSEProgressData {
  type: 'initial' | 'step_update' | 'complete' | 'error' | 'heartbeat';
  step?: {
    id: string;
    title: string;
    description?: string;
    status: 'pending' | 'in_progress' | 'completed' | 'error';
    timestamp: string;
    details?: string[];
  };
  steps?: Array<{
    id: string;
    title: string;
    description?: string;
    status: 'pending' | 'in_progress' | 'completed' | 'error';
    timestamp: string;
    details?: string[];
  }>;
  isComplete?: boolean;
  error?: string;
  result?: any;
}

export function useOrchestrationSSE(sessionId: string | null) {
  const [steps, setSteps] = useState<ProgressStep[]>([]);
  const [isActive, setIsActive] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const convertToProgressStep = useCallback((step: any): ProgressStep => {
    return {
      id: step.id,
      title: step.title,
      description: step.description,
      status: step.status,
      timestamp: new Date(step.timestamp),
      details: step.details,
      metadata: step.metadata,
      icon: getIconForStep(step.id)
    };
  }, []);

  const getIconForStep = useCallback((stepId: string): ProgressStep['icon'] => {
    if (stepId.includes('analyzing') || stepId.includes('classification')) return 'analysis';
    if (stepId.includes('specialist') || stepId.includes('role')) return 'roles';
    if (stepId.includes('orchestration') || stepId.includes('workflow')) return 'workflow';
    if (stepId.includes('agent') || stepId.includes('creating')) return 'agents';
    if (stepId.includes('supervisor')) return 'supervisor';
    if (stepId.includes('planning') || stepId.includes('task')) return 'planning';
    if (stepId.includes('working') || stepId.includes('agent_')) return 'working';
    if (stepId.includes('synthesis') || stepId.includes('combining')) return 'synthesis';
    if (stepId.includes('generating') || stepId.includes('response')) return 'generating';
    return 'connecting';
  }, []);

  const connectToSSE = useCallback(() => {
    if (!sessionId || eventSourceRef.current) return;

    console.log(`[SSE] Connecting to orchestration progress: ${sessionId}`);
    
    try {
      const eventSource = new EventSource(`/api/orchestration/progress?sessionId=${sessionId}`);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('[SSE] Connected to orchestration progress stream');
        reconnectAttempts.current = 0;
        setIsActive(true);
        setError(null);
      };

      eventSource.onmessage = (event) => {
        try {
          const data: SSEProgressData = JSON.parse(event.data);
          console.log('[SSE] Received progress data:', data);

          switch (data.type) {
            case 'initial':
              if (data.steps) {
                setSteps(data.steps.map(convertToProgressStep));
              }
              if (data.isComplete) {
                setIsComplete(true);
                setIsActive(false);
              }
              if (data.error) {
                setError(data.error);
              }
              break;

            case 'step_update':
              if (data.step) {
                const newStep = convertToProgressStep(data.step);
                setSteps(prev => {
                  const existingIndex = prev.findIndex(s => s.id === newStep.id);
                  if (existingIndex >= 0) {
                    // Update existing step
                    const updated = [...prev];
                    updated[existingIndex] = newStep;
                    return updated;
                  } else {
                    // Add new step
                    return [...prev, newStep];
                  }
                });
              }
              break;

            case 'complete':
              console.log('[SSE] Orchestration completed');
              setIsComplete(true);
              setIsActive(false);
              // Keep connection open briefly to show final state
              setTimeout(() => {
                eventSource.close();
              }, 3000);
              break;

            case 'error':
              console.error('[SSE] Orchestration error:', data.error);
              setError(data.error || 'Unknown error occurred');
              setIsActive(false);
              break;

            case 'heartbeat':
              // Just keep the connection alive
              break;

            default:
              console.warn('[SSE] Unknown message type:', data.type);
          }
        } catch (parseError) {
          console.error('[SSE] Error parsing progress data:', parseError);
        }
      };

      eventSource.onerror = (error) => {
        console.error('[SSE] Connection error:', error);
        eventSource.close();
        eventSourceRef.current = null;

        // Attempt to reconnect with exponential backoff
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 10000);
          console.log(`[SSE] Reconnecting in ${delay}ms (attempt ${reconnectAttempts.current + 1}/${maxReconnectAttempts})`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connectToSSE();
          }, delay);
        } else {
          console.error('[SSE] Max reconnection attempts reached');
          setError('Connection lost. Please refresh the page.');
          setIsActive(false);
        }
      };

    } catch (error) {
      console.error('[SSE] Failed to create EventSource:', error);
      setError('Failed to connect to progress stream');
    }
  }, [sessionId, convertToProgressStep]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      console.log('[SSE] Disconnecting from orchestration progress');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setIsActive(false);
  }, []);

  const reset = useCallback(() => {
    disconnect();
    setSteps([]);
    setIsComplete(false);
    setError(null);
    reconnectAttempts.current = 0;
  }, [disconnect]);

  // Connect when sessionId is provided
  useEffect(() => {
    if (sessionId) {
      connectToSSE();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [sessionId, connectToSSE, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    steps,
    isActive,
    isComplete,
    error,
    connect: connectToSSE,
    disconnect,
    reset
  };
}
