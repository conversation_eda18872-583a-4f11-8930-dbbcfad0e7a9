'use client';

import React, { useEffect, useCallback } from 'react';
import OrchestrationProgressTracker from './OrchestrationProgressTracker';
import { useOrchestrationTracking, ORCHESTRATION_STEPS } from '@/hooks/useOrchestrationTracking';
import { type ProgressCallback } from '@/lib/langgraph-orchestration/RouKeyLangGraphIntegration';

interface OrchestrationProgressBridgeProps {
  isActive: boolean;
  onProgressCallback?: (callback: ProgressCallback) => void;
  className?: string;
}

export default function OrchestrationProgressBridge({
  isActive,
  onProgressCallback,
  className = ''
}: OrchestrationProgressBridgeProps) {
  const {
    steps,
    isActive: trackingActive,
    trackEvent,
    completeStep,
    startTracking,
    completeTracking,
    resetTracking
  } = useOrchestrationTracking();

  // Create progress callback for orchestration system
  const progressCallback: ProgressCallback = useCallback({
    onClassificationStart: () => {
      trackEvent(ORCHESTRATION_STEPS.MULTI_ROLE.CLASSIFICATION);
    },
    
    onClassificationComplete: (roles: string[], threshold: number) => {
      completeStep('classification', [
        `Detected roles: ${roles.join(', ')}`,
        `Applied threshold: ${threshold}`,
        `Classification completed successfully`
      ]);
    },
    
    onRoleSelectionComplete: (selectedRoles: string[], filteredRoles: string[]) => {
      trackEvent({
        ...ORCHESTRATION_STEPS.MULTI_ROLE.ROLE_SELECTION,
        details: [
          `Selected roles: ${selectedRoles.join(', ')}`,
          ...(filteredRoles.length > 0 ? [`Filtered roles: ${filteredRoles.join(', ')}`] : []),
          `${selectedRoles.length} roles qualified for orchestration`
        ]
      });
      setTimeout(() => completeStep('role_selection'), 100);
    },
    
    onWorkflowSelectionComplete: (workflowType: string, reasoning: string) => {
      trackEvent({
        ...ORCHESTRATION_STEPS.MULTI_ROLE.WORKFLOW_SELECTION,
        description: `${workflowType.charAt(0).toUpperCase() + workflowType.slice(1)} workflow selected`,
        details: [
          `Workflow type: ${workflowType}`,
          `Reasoning: ${reasoning}`
        ]
      });
      setTimeout(() => completeStep('workflow_selection'), 100);
    },
    
    onAgentCreationStart: () => {
      trackEvent(ORCHESTRATION_STEPS.MULTI_ROLE.AGENT_CREATION);
    },
    
    onAgentCreationComplete: (agents: Array<{ role: string, apiKey: string }>) => {
      completeStep('agent_creation', [
        `Created ${agents.length} specialized agents:`,
        ...agents.map(agent => `• ${agent.role}: ${agent.apiKey}`)
      ]);
    },
    
    onSupervisorInitStart: () => {
      trackEvent(ORCHESTRATION_STEPS.MULTI_ROLE.SUPERVISOR_INIT);
    },
    
    onSupervisorInitComplete: (supervisorRole: string) => {
      completeStep('supervisor_init', [
        `Supervisor role: ${supervisorRole}`,
        'Coordination system initialized',
        'Ready to manage agent workflow'
      ]);
    },
    
    onTaskPlanningStart: () => {
      trackEvent(ORCHESTRATION_STEPS.MULTI_ROLE.TASK_PLANNING);
    },
    
    onTaskPlanningComplete: (plan: string) => {
      completeStep('task_planning', [
        'Task distribution strategy:',
        plan,
        'Agents ready to begin work'
      ]);
    },
    
    onAgentWorkStart: (role: string, task: string) => {
      trackEvent({
        type: 'agent_working',
        stepId: `agent_${role}`,
        title: `${role.charAt(0).toUpperCase() + role.slice(1)} Agent Working`,
        description: task,
        details: [
          `Role: ${role}`,
          `Task: ${task}`,
          'Processing request...'
        ]
      });
    },
    
    onAgentWorkComplete: (role: string, result: string) => {
      completeStep(`agent_${role}`, [
        `Completed ${role} work`,
        `Generated ${result.length} characters`,
        'Ready for next step'
      ]);
    },
    
    onSupervisorSynthesisStart: () => {
      trackEvent({
        type: 'supervisor_synthesis',
        stepId: 'supervisor_synthesis',
        title: 'Supervisor: Combining Results',
        description: 'Creating comprehensive final response...',
        details: [
          'Analyzing all agent contributions',
          'Synthesizing final response',
          'Ensuring coherent output'
        ]
      });
    },
    
    onSupervisorSynthesisComplete: (synthesis: string) => {
      completeStep('supervisor_synthesis', [
        'Final synthesis completed',
        `Generated ${synthesis.length} characters`,
        'Multi-role orchestration successful'
      ]);
    },
    
    onOrchestrationComplete: (result: any) => {
      completeTracking();
    },
    
    onError: (step: string, error: string) => {
      trackEvent({
        type: 'error',
        stepId: `error_${step}`,
        title: `Error in ${step}`,
        description: error,
        details: [
          `Step: ${step}`,
          `Error: ${error}`,
          'Attempting recovery...'
        ]
      });
    }
  }, [trackEvent, completeStep, completeTracking]);

  // Provide progress callback to parent
  useEffect(() => {
    if (onProgressCallback) {
      onProgressCallback(progressCallback);
    }
  }, [onProgressCallback, progressCallback]);

  // Start/stop tracking based on isActive
  useEffect(() => {
    if (isActive && !trackingActive) {
      startTracking();
    } else if (!isActive && trackingActive) {
      // Small delay before resetting to show final state
      setTimeout(() => {
        resetTracking();
      }, 3000);
    }
  }, [isActive, trackingActive, startTracking, resetTracking]);

  if (!trackingActive || steps.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      <OrchestrationProgressTracker
        steps={steps}
        isActive={trackingActive}
        autoScroll={true}
      />
    </div>
  );
}
