'use client';

import React, { useState, useEffect } from 'react';
import {
  ChevronDownIcon,
  ChevronRightIcon,
  CheckCircleIcon,
  CogIcon,
  SparklesIcon,
  UserGroupIcon,
  BoltIcon,
  PencilIcon,
  BeakerIcon,
  RocketLaunchIcon,
  CommandLineIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  CpuChipIcon,
  LightBulbIcon,
  FireIcon
} from '@heroicons/react/24/outline';

export interface ProgressStep {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error';
  timestamp?: Date;
  details?: string[];
  metadata?: Record<string, any>;
  icon?: 'analysis' | 'roles' | 'workflow' | 'agents' | 'supervisor' | 'planning' | 'working' | 'synthesis' | 'connecting' | 'generating';
}

const STEP_ICONS = {
  analysis: MagnifyingGlassIcon,
  roles: UserGroupIcon,
  workflow: ArrowPathIcon,
  agents: CpuChipI<PERSON>,
  supervisor: SparklesI<PERSON>,
  planning: LightBulbIcon,
  working: CogIcon,
  synthesis: BeakerIcon,
  connecting: BoltIcon,
  generating: PencilIcon
};

const STEP_COLORS = {
  pending: {
    bg: 'bg-gray-900/80',
    border: 'border-gray-700/60',
    icon: 'text-gray-400',
    text: 'text-gray-300'
  },
  in_progress: {
    bg: 'bg-gradient-to-r from-orange-900/60 to-orange-800/60',
    border: 'border-orange-500/40',
    icon: 'text-orange-300',
    text: 'text-orange-100'
  },
  completed: {
    bg: 'bg-gradient-to-r from-green-900/60 to-emerald-900/60',
    border: 'border-green-500/40',
    icon: 'text-green-300',
    text: 'text-green-100'
  },
  error: {
    bg: 'bg-gradient-to-r from-red-900/60 to-red-800/60',
    border: 'border-red-500/40',
    icon: 'text-red-300',
    text: 'text-red-100'
  }
};

interface OrchestrationProgressCardProps {
  step: ProgressStep;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  className?: string;
}

export default function OrchestrationProgressCard({
  step,
  isExpanded = false,
  onToggleExpand,
  className = ''
}: OrchestrationProgressCardProps) {
  const [isAnimating, setIsAnimating] = useState(false);
  
  const colors = STEP_COLORS[step.status];
  const IconComponent = step.icon ? STEP_ICONS[step.icon] : CogIcon;
  const ChevronIcon = isExpanded ? ChevronDownIcon : ChevronRightIcon;

  // Animate status changes
  useEffect(() => {
    if (step.status === 'completed' || step.status === 'in_progress') {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 600);
      return () => clearTimeout(timer);
    }
  }, [step.status]);

  const handleToggle = () => {
    if (onToggleExpand && (step.details?.length || step.description)) {
      onToggleExpand();
    }
  };

  return (
    <div className={`transition-all duration-300 ${className}`}>
      <div
        className={`
          relative rounded-xl border backdrop-blur-sm transition-all duration-300
          ${colors.bg} ${colors.border}
          ${step.status === 'in_progress' ? 'shadow-lg shadow-orange-500/30 ring-1 ring-orange-500/20' : ''}
          ${step.status === 'completed' ? 'shadow-lg shadow-green-500/30 ring-1 ring-green-500/20' : ''}
          ${step.status === 'error' ? 'shadow-lg shadow-red-500/30 ring-1 ring-red-500/20' : ''}
          ${isAnimating ? 'scale-[1.02] shadow-xl' : 'scale-100'}
          hover:shadow-xl hover:scale-[1.01]
        `}
      >
        {/* Main card content */}
        <div 
          className={`
            flex items-center p-4 cursor-pointer transition-all duration-200
            ${(step.details?.length || step.description) ? 'hover:bg-white/5' : ''}
          `}
          onClick={handleToggle}
        >
          {/* Status icon with spinner for in-progress */}
          <div className="flex-shrink-0 mr-3">
            {step.status === 'completed' ? (
              <CheckCircleIcon className={`w-5 h-5 ${colors.icon} transition-colors duration-300`} />
            ) : step.status === 'in_progress' ? (
              <div className="relative">
                <IconComponent className={`w-5 h-5 ${colors.icon} transition-colors duration-300`} />
                <div className="absolute inset-0 border-2 border-transparent border-t-orange-300 rounded-full animate-spin opacity-80" />
                <div className="absolute inset-1 border border-transparent border-t-orange-400 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }} />
              </div>
            ) : step.status === 'error' ? (
              <div className={`w-5 h-5 rounded-full border-2 ${colors.border} flex items-center justify-center`}>
                <div className={`w-2 h-2 rounded-full bg-red-400`} />
              </div>
            ) : (
              <div className={`w-5 h-5 rounded-full border-2 ${colors.border}`} />
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h3 className={`text-sm font-medium ${colors.text} transition-colors duration-300`}>
                {step.title}
              </h3>
              
              {/* Timestamp */}
              {step.timestamp && (
                <span className="text-xs text-gray-500 ml-2">
                  {step.timestamp.toLocaleTimeString()}
                </span>
              )}
            </div>
            
            {/* Description (always visible if present) */}
            {step.description && (
              <p className="text-xs text-gray-400 mt-1 leading-relaxed">
                {step.description}
              </p>
            )}
          </div>

          {/* Expand chevron */}
          {(step.details?.length || step.description) && (
            <div className="flex-shrink-0 ml-2">
              <ChevronIcon className={`w-4 h-4 ${colors.icon} transition-transform duration-200`} />
            </div>
          )}
        </div>

        {/* Expandable details */}
        {isExpanded && step.details?.length && (
          <div className="border-t border-gray-700 px-4 py-3 bg-black/20">
            <div className="space-y-2">
              {step.details.map((detail, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <div className="w-1 h-1 rounded-full bg-gray-500 mt-2 flex-shrink-0" />
                  <p className="text-xs text-gray-400 leading-relaxed">{detail}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Progress bar for in-progress items */}
        {step.status === 'in_progress' && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-800/50 rounded-b-xl overflow-hidden">
            <div className="h-full bg-gradient-to-r from-orange-500 via-orange-400 to-orange-500 relative overflow-hidden"
                 style={{
                   width: '70%',
                   animation: 'progressShimmer 2s linear infinite, progressPulse 1.5s ease-in-out infinite'
                 }}>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
