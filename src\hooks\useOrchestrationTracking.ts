'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { ProgressStep } from '@/components/OrchestrationProgressCard';

export interface OrchestrationEvent {
  type: 'classification' | 'role_selection' | 'workflow_selection' | 'agent_creation' | 
        'supervisor_init' | 'task_planning' | 'agent_working' | 'supervisor_synthesis' | 
        'orchestration_complete' | 'error';
  stepId: string;
  title: string;
  description?: string;
  details?: string[];
  metadata?: Record<string, any>;
  timestamp?: Date;
}

export function useOrchestrationTracking() {
  const [steps, setSteps] = useState<ProgressStep[]>([]);
  const [isActive, setIsActive] = useState(false);
  const eventQueueRef = useRef<OrchestrationEvent[]>([]);
  const processingRef = useRef(false);

  // Process events from the queue
  const processEventQueue = useCallback(async () => {
    if (processingRef.current || eventQueueRef.current.length === 0) return;
    
    processingRef.current = true;
    
    while (eventQueueRef.current.length > 0) {
      const event = eventQueueRef.current.shift()!;
      
      // Add small delay for visual effect
      await new Promise(resolve => setTimeout(resolve, 150));
      
      setSteps(prev => {
        const existingStepIndex = prev.findIndex(step => step.id === event.stepId);
        
        if (existingStepIndex >= 0) {
          // Update existing step
          const updatedSteps = [...prev];
          updatedSteps[existingStepIndex] = {
            ...updatedSteps[existingStepIndex],
            status: event.type === 'error' ? 'error' : 'completed',
            description: event.description || updatedSteps[existingStepIndex].description,
            details: event.details || updatedSteps[existingStepIndex].details,
            timestamp: event.timestamp || new Date(),
            metadata: { ...updatedSteps[existingStepIndex].metadata, ...event.metadata }
          };
          return updatedSteps;
        } else {
          // Add new step
          const newStep: ProgressStep = {
            id: event.stepId,
            title: event.title,
            description: event.description,
            status: event.type === 'error' ? 'error' : 'in_progress',
            timestamp: event.timestamp || new Date(),
            details: event.details,
            metadata: event.metadata,
            icon: getIconForEventType(event.type)
          };
          return [...prev, newStep];
        }
      });
    }
    
    processingRef.current = false;
  }, []);

  // Add event to queue and process
  const trackEvent = useCallback((event: OrchestrationEvent) => {
    eventQueueRef.current.push(event);
    setIsActive(true);
    processEventQueue();
  }, [processEventQueue]);

  // Complete a step
  const completeStep = useCallback((stepId: string, details?: string[]) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, status: 'completed' as const, details, timestamp: new Date() }
        : step
    ));
  }, []);

  // Start orchestration tracking
  const startTracking = useCallback(() => {
    setSteps([]);
    setIsActive(true);
    eventQueueRef.current = [];
  }, []);

  // Complete orchestration tracking
  const completeTracking = useCallback(() => {
    // Mark any in-progress steps as completed
    setSteps(prev => prev.map(step => 
      step.status === 'in_progress' 
        ? { ...step, status: 'completed' as const, timestamp: new Date() }
        : step
    ));
    
    // Keep active for a bit longer to show final state
    setTimeout(() => {
      setIsActive(false);
    }, 2000);
  }, []);

  // Reset tracking
  const resetTracking = useCallback(() => {
    setSteps([]);
    setIsActive(false);
    eventQueueRef.current = [];
    processingRef.current = false;
  }, []);

  return {
    steps,
    isActive,
    trackEvent,
    completeStep,
    startTracking,
    completeTracking,
    resetTracking
  };
}

// Helper function to map event types to icons
function getIconForEventType(eventType: OrchestrationEvent['type']): ProgressStep['icon'] {
  switch (eventType) {
    case 'classification':
      return 'analysis';
    case 'role_selection':
      return 'roles';
    case 'workflow_selection':
      return 'workflow';
    case 'agent_creation':
      return 'agents';
    case 'supervisor_init':
      return 'supervisor';
    case 'task_planning':
      return 'planning';
    case 'agent_working':
      return 'working';
    case 'supervisor_synthesis':
      return 'synthesis';
    case 'orchestration_complete':
      return 'generating';
    default:
      return 'working';
  }
}

// Predefined step templates for common orchestration flows
export const ORCHESTRATION_STEPS = {
  // Multi-role orchestration steps
  MULTI_ROLE: {
    CLASSIFICATION: {
      type: 'classification' as const,
      stepId: 'classification',
      title: 'Analyzing request with RouKey AI',
      description: 'Determining the best approach for your request...'
    },
    ROLE_SELECTION: {
      type: 'role_selection' as const,
      stepId: 'role_selection',
      title: 'Selecting AI Specialists',
      description: 'Choosing the right experts for your task...'
    },
    WORKFLOW_SELECTION: {
      type: 'workflow_selection' as const,
      stepId: 'workflow_selection',
      title: 'Planning Collaboration Strategy',
      description: 'Organizing how specialists will work together...'
    },
    AGENT_CREATION: {
      type: 'agent_creation' as const,
      stepId: 'agent_creation',
      title: 'Preparing AI Specialists',
      description: 'Setting up your dedicated team of experts...'
    },
    SUPERVISOR_INIT: {
      type: 'supervisor_init' as const,
      stepId: 'supervisor_init',
      title: 'Team Coordination Setup',
      description: 'Organizing collaboration between specialists...'
    },
    TASK_PLANNING: {
      type: 'task_planning' as const,
      stepId: 'task_planning',
      title: 'Work Distribution Planning',
      description: 'Planning how tasks will be distributed...'
    }
  },
  
  // Single role orchestration steps
  SINGLE_ROLE: {
    CLASSIFICATION: {
      type: 'classification' as const,
      stepId: 'classification',
      title: 'Analyzing request with RouKey AI',
      description: 'Understanding your request and requirements...'
    },
    WORKFLOW_SELECTION: {
      type: 'workflow_selection' as const,
      stepId: 'workflow_selection',
      title: 'Direct Processing Selected',
      description: 'Using focused approach for your request...'
    },
    AGENT_CREATION: {
      type: 'agent_creation' as const,
      stepId: 'agent_creation',
      title: 'Preparing AI Specialist',
      description: 'Setting up the perfect expert for your task...'
    }
  }
};
